import { Injectable, InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Op } from "sequelize";
import { Candidate } from "../candidates/candidates.model";
import { Industry } from "../industries/industries.model";
import { ScoreboardFilterDto } from "../job/dto/filter-job.dto";
import { Jobs } from "../job/job.model";
import { Location } from "../locations/location.model";
import { Position } from "../positions/positions.model";
import { RoundService } from "../rounds/round.service";
import { Skill } from "../skills/skills.model";
import { User } from "../users/users.model";
import { UsersService } from "../users/users.service";
import { CreateSubscribeDto, StartWorkflowDto } from "./dto/create-subscribe.dto";
import { v4 as uuidv4 } from "uuid";

import {
  EditJobsStatus,
  EditStatusDto,
  EditSubscribeDto,
  RestartWorkflowDto,
} from "./dto/edit-subscribe.dto";
import { Subscribe } from "./subscribe.model";
import { Workflow } from "../workflow/workflow.model";
import { Company } from "../companies/companies.model";
import { FilterSubscribeDto } from "./dto/filter-subscribe.dto";
import { Round } from "../rounds/round.model";
import { roundStatus } from "../rounds/enum/roundStatus.enum";
import { WorkflowService } from "../workflow/workflow.service";
import { HttpService } from "@nestjs/axios";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { recruitmentWorkflow } from "apps/temporal/src/app/workflow/temporal/workflows";
import { TemporalWorkflowService } from "../temporalWorkflow/temporal.service";
import { CandidatesService } from "../candidates/candidates.service";
import { CandidateFiltersDto } from "../candidates/dto/candidate-filters.dto";
import { UserAssignmentsService } from "../user-assignments/user-assignments.service";
import { EmailService } from "@microservices/email";
import { JobTargetService } from "../jobtarget/jobtarget.service";

@Injectable()
export class SubscribeService {
  constructor(
    @InjectModel(Subscribe) private subscribeRepository: typeof Subscribe,
    @InjectModel(Candidate) private profileRepository: typeof Candidate,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    private roundService: RoundService,
    private userService: UsersService,
    private workflowService: WorkflowService,
    private httpService: HttpService,
    private candidateService: CandidatesService,
    private temporalWorkflowService: TemporalWorkflowService,
    private userAssignmentsService: UserAssignmentsService,
    private emailService: EmailService,
    private jobTargetService: JobTargetService,
  ) { }
  async createSubscribe(dto: CreateSubscribeDto, userId: number, isAssessment = false) {
    try {
      const [data] = await this.subscribeRepository.findOrCreate({
        where: {
          userId,
          jobId: dto.jobId,
        },
      });
      const match = isAssessment ? 0 : await this.match(userId, dto.jobId);
      const jobs = await this.jobRepository.findOne({
        where: {
          id: dto.jobId,
        },
        include: [Workflow, User,Company],
      });
      let round;
      const userData = await this.userService.getUserByUserId(userId);
      if (dto.applyJob && jobs) {
        await this.emailService.sendRecruitmentActivityService("jobDetail.html",
          {
            companyLogo: `${jobs?.company?.avatar || ""}`,
            position: `New Candidate Application for ${jobs?.title}`,
            body: `
            <p>Dear ${jobs?.author?.firstname} ${jobs?.author?.lastname}</p>
            <p>We would like to inform you that we have received an application for the position of ${jobs?.title} at ${jobs?.company?.name}.</p>
            <p>The applicant, ${userData?.firstname} ${userData?.lastname}, has expressed a strong interest in joining your team.</p>
            <p>Please review their profile, including their resume and any supporting documents, and initiate the hiring workflow if their qualifications align with your requirements.</p>
            <br/>
            <a href="${process.env.WEB_APP_URI + `/recruitment/job/${jobs.id}/matched-profiles`}"
            style="
              text-decoration: none;
              color: inherit;
              display: block;
              width: 100%;
              height: 100%;
            ">
            <button style="
            font-size: 14px;
            font-weight: 900;
            line-height: 100%;
            color: white;
            padding: 16px 28px;
            border: none;
            cursor: pointer;
            background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
            border-radius: 4px;
            ">View Candidate profile</button>
            </a>
            `,
            userId: jobs?.authorId,
          },
          jobs.company.email,
          `${userData?.firstname} ${userData?.lastname} has applied for ${jobs?.title} Job position at ${jobs?.company?.name} company.`,
          { ...jobs.dataValues,
            link:`/recruitment/job/${jobs?.id}/matched-profiles`,
            notificationTitle:'New Candidate Application Received',
            notificationMsg:`${userData?.firstname} ${userData?.lastname} has applied for ${jobs?.title} Job position at ${jobs?.company?.name} company.`,
         })

         await this.emailService.sendRecruitmentActivityService(
          "jobDetail.html",
          {
            companyLogo: `${jobs?.company?.avatar || ""}`,
            position: `Your Application for ${jobs?.title} Has Been Received`,
            body: `
              <p>Dear ${userData?.firstname} ${userData?.lastname},</p>
              <p>Thank you for applying for the position of <strong>${jobs?.title}</strong> at <strong>${jobs?.company?.name}</strong>. We have successfully received your application.</p>
              <p>The company will review your profile, and we will notify you once there is any update regarding the next steps.</p>
              <p>If you have any questions in the meantime, feel free to reach out.</p>
              <br/>
            `,
            userId: userData?.id,
          },
          userData?.email,
          `Your application for ${jobs?.title} at ${jobs?.company?.name} has been received.`,
          {
            ...userData.dataValues,
            notificationTitle: "Application Successfully Submitted",
            notificationMsg: `Your application for ${jobs?.title} at ${jobs?.company?.name} has been successfully submitted. We'll keep you updated on any further steps.`,
          }
        );
      }

      Object.assign(data, {
        ...dto,
        userId: data.userId,
        jobId: data.jobId,
        status:"Pending",
        match: Number(match),
      });
      const result = await data.save();
      return {
        ...result,
        jobs: jobs,
        userData,
      };
    } catch (err) {
      Logger.log("Error : ", err);
    }
  }

  async restartWorkflow(dto: RestartWorkflowDto) {
    const job = await this.jobRepository.findOne({ where: { id: dto.jobId } })
    const userData = await this.userService.getUserByUserId(dto.userId);
    let newRound: any = {}
    let updatedWorkflow = [];
    let updatedRounds = [];
    if (job && job.workflowId) {
      let workflows = await this.workflowService.getOne(job.workflowId, job.companyId)
      if (workflows && workflows.workflow) {
        const round = await this.roundService.getOne(dto.userId, dto.jobId)
        const rounds = round?.round?.rounds
        if (rounds) {
          const rejectedRound = rounds.findLast(i => i.status === roundStatus.REJECTED)
          if (rejectedRound) {
            const newWorkflowIndex = workflows.workflow.findIndex(i => i.name === rejectedRound.title)
            if (workflows.workflow[newWorkflowIndex]) {

              updatedWorkflow = workflows.workflow?.slice(newWorkflowIndex, Infinity)
              for (let i in rounds) {
                if (rounds[i].status === roundStatus.REJECTED && rounds[+i + 1]?.status !== roundStatus.RESTARTED) {
                  updatedRounds.push({ ...rounds[i], title: `${rounds[i].title}(rejected)` })
                  updatedRounds.push({ status: "Restarted" })
                  updatedRounds.push({ ...rounds[i], status: roundStatus.PENDING, date:{start:null,end:null}})
                } else {
                  updatedRounds.push(rounds[i])
                }
              }
              await this.roundService.delete(job.id, dto.userId)
              newRound = await this.roundService.create({ jobId: job.id, userId: dto.userId, rounds: updatedRounds })
            }
          }
        }
      }
      const subscribe = await this.subscribeRepository.findOne({ where: { jobId: dto.jobId, userId: dto.userId } })
      Object.assign(subscribe, { status: "In Progress", ...(newRound?.id && { roundId: newRound?.id }) })
      await subscribe.save()

      // JobTarget tracking: Update applicant status to "Application Viewed" when workflow starts
      if (subscribe.JT_GUID && job?.author?.email) {
        try {
          await this.jobTargetService.updateApplicantStatus(
            subscribe.JT_GUID,
            "Application Viewed",
            job.author.email
          );
          Logger.log(`JobTarget applicant status updated for GUID: ${subscribe.JT_GUID}`);
        } catch (error) {
          Logger.error(`Failed to update JobTarget applicant status: ${error.message}`);
        }
      }

      Object.assign(workflows, {
        ...workflows,
        workflow: updatedWorkflow
      })
      return {
        subscribeId: subscribe.id,
        userData,
        job,
        workflowData: workflows
      }
    }


  }
  async editSubscribe(dto: EditSubscribeDto, userId: number) {
    const data = await this.subscribeRepository.findOne({
      where: {
        userId,
        jobId: dto.jobId,
        id: dto.id,
      },
    });
    const match = await this.match(userId, dto.jobId);
    Object.assign(data, {
      ...dto,
      userId: data.userId,
      jobId: data.jobId,
      match: match,
    });
    return data.save();
  }

  async match(userId: number, jobId: number) {
    let match = 0;
    const candidate = await this.profileRepository.findOne({
      where: { userId },
      attributes: [
        "locationId",
        "remoteLocation",
        "experience",
        "degree",
        "positionId",
        "preferencesExpectedCTC",
      ],
      include: [
        {
          model: Skill,
          attributes: ["name"],
        },
      ],
    });
    const job = await this.jobRepository.findOne({
      where: { id: jobId },
      attributes: [
        "remoteLocation",
        "experienceMin",
        "experienceMax",
        "education",
        "positionId",
        "salaryYearMin",
        "salaryYearMax",
        "skills",
      ],
      include: [
        {
          model: Location,
          attributes: ["id"],
        },
      ],
    });
    job.locations.find((value) => {
      if (value.id === candidate.locationId) {
        match += 10;
      }
    });
    if (candidate.remoteLocation == job.remoteLocation) {
      match += 5;
    }
    if (
      candidate.experience >= job.experienceMin &&
      candidate.experience <= job.experienceMax
    ) {
      match += 10;
    }
    if (
      candidate.preferencesExpectedCTC >= job.salaryYearMin &&
      candidate.preferencesExpectedCTC <= job.salaryYearMax
    ) {
      match += 15;
    }
    if (candidate.degree == job.education) {
      match += 10;
    }
    if (candidate.positionId == job.positionId) {
      match += 20;
    }
    const jobSkills = job.skills;
    const countSkill = jobSkills.length;
    const oneSkill = 30 / parseInt(countSkill);
    candidate.skills.forEach((value) => {
      jobSkills.find((item) => {
        if (item.number == value.name) {
          match += oneSkill;
        }
      });
    });

    return Number(match).toFixed(2);
  }

  async getScoreboard(id: number, companyId: number, dto: ScoreboardFilterDto) {
    try {
      const where: any = {};
      const whereUser: any = {};
      const whereCandidate: any = {};
      const whereLoc: any = {};
      let requiredLoc = false;
      let requiredSkills = false;
      const whereSkills: any = {};
      const order = [];
      if (dto.search) {
        whereUser[Op.or] = [
          {
            firstname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
          {
            lastname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
          {
            middlename: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
        ];
      }
      if (dto.locations) {
        requiredLoc = true;
        if (dto.locations instanceof Array) {
          whereCandidate.locationId = {
            [Op.in]: dto.locations,
          };
        } else {
          whereCandidate.locationId = {
            [Op.eq]: dto.locations,
          };
        }
      }
      if (dto.experienceMin && dto.experienceMax) {
        whereCandidate.experience = {
          [Op.between]: [dto.experienceMin, dto.experienceMax],
        };
      } else if (dto.experienceMin || dto.experienceMax) {
        if (dto.experienceMin) {
          whereCandidate.experience = {
            [Op.gte]: dto.experienceMin,
          };
        }
        if (dto.experienceMax) {
          whereCandidate.experience = {
            [Op.lte]: dto.experienceMax,
          };
        }
      }
      if (dto.salaryYearMin && dto.salaryYearMax) {
        whereCandidate.preferencesExpectedCTC = {
          [Op.between]: [dto.salaryYearMin, dto.salaryYearMax],
        };
      } else if (dto.salaryYearMin || dto.salaryYearMax) {
        if (dto.salaryYearMin) {
          whereCandidate.preferencesExpectedCTC = {
            [Op.gte]: dto.salaryYearMin,
          };
        }
        if (dto.salaryYearMax) {
          whereCandidate.preferencesExpectedCTC = {
            [Op.lte]: dto.salaryYearMax,
          };
        }
      }

      if (dto.degree) {
        if (dto.degree instanceof Array) {
          whereCandidate.degree = {
            [Op.iLike]: { [Op.any]: dto.degree },
          };
        } else {
          whereCandidate.degree = {
            [Op.iLike]: `%${dto.degree}%`,
          };
        }
      }

      if (dto.skills) {
        requiredSkills = true;
        if (dto.skills instanceof Array) {
          whereSkills[Op.or] = [
            {
              name: {
                [Op.iLike]: { [Op.any]: dto.skills },
              },
            },
          ];
        } else {
          whereSkills.name = {
            [Op.iLike]: `%${dto.skills}%`,
          };
        }
      }

      if (dto.percentageMin && dto.percentageMax) {
        where.match = {
          [Op.between]: [dto.percentageMin, dto.percentageMax],
        };
      } else if (dto.percentageMin || dto.percentageMax) {
        if (dto.percentageMin) {
          where.match = {
            [Op.gte]: dto.percentageMin,
          };
        }
        if (dto.percentageMax) {
          where.match = {
            [Op.lte]: dto.percentageMax,
          };
        }
      }

      if (dto.sortBy && dto.sortType) {
        order.push([dto.sortBy, dto.sortType]);
      } else {
        order.push(["id", "DESC"]);
      }

      if(dto.applyJob && dto.applyJob === "applyJob"){
        where.status = {
          [Op.not]:["Pending"]
        }
      }
        where.applyJob = true;

      return {
        scoreboard: await this.subscribeRepository.findAndCountAll({
          include: [
            {
              model: Jobs,
              attributes: ["id", "workflowId", "companyId"],
              where: {
                id,
                companyId,
              },
            },
            {
              model:Round,
              attributes:["id","rounds"],
            },
            {
              model: User,
              attributes: [
                "id",
                "email",
                "avatar",
                "firstname",
                "middlename",
                "lastname",
                "phone",
              ],
              where: whereUser,
              include: [
                {
                  model: Candidate,
                  attributes: [
                    "id",
                    "experience",
                    "currentCtc",
                    "preferencesExpectedCTC",
                    "degree",
                    "locationId",
                  ],
                  where: whereCandidate,
                  include: [
                    {
                      model: Location,
                      attributes: ["id", "city", "state", "st"],
                      where: whereLoc,
                      required: requiredLoc,
                    },
                    {
                      model: Skill,
                      where: whereSkills,
                      attributes: ["name", "id", "years"],
                      required: requiredSkills,
                    },
                  ],
                },
              ],
            },
          ],
          order,
          where,
          distinct: true,
          attributes: [
            "id",
            "jobId",
            "userId",
            "match",
            "saveJob",
            "applyJob",
            "subscribeJob",
            "status",
            "totalScores",
            "summary",
          ],
          limit: dto.limit,
          offset: dto.offset,
        }),
        job: await this.jobRepository.findOne({
          attributes: ["id", "companyId", "title"],
          where: {
            id,
            companyId,
          },
        }),
      };
    } catch (error) {
      throw new NotFoundException(`Not found`);
    }
  }

  async getScoreboards(companyId: number, dto: any) {

    let whereUser: any = {};
    let where: any = {};
    let order: any[] = []
    if (dto.search) {
      whereUser[Op.or] = [
        {
          firstname: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
        {
          lastname: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
        {
          middlename: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
      ];
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "ASC"]);
    }

    if (dto.jobId) {
      if (typeof dto.jobId === 'object' && Array.isArray(dto.jobId)) {
        where.jobId = {
          [Op.or]: dto.jobId
        }
      } else {
        where.jobId = dto.jobId;
      }
    }

    if (dto.totalScoreMin && dto.totalScoreMax) {
      where.totalScores = {
        [Op.between]: [dto.totalScoreMin, dto.totalScoreMax]
      }
    } else if (dto.totalScoreMin) {
      where.totalScores = {
        [Op.gte]: dto.totalScoreMin
      }
    } else if (dto.totalScoreMax) {
      where.totalScores = {
        [Op.lte]: dto.totalScoreMax
      }
    }

    const data = await this.subscribeRepository.findAndCountAll({
      where: {
        applyJob: true,
        roundId: {
          [Op.not]: null
        },
        ...where
      },
      include: [
        {
          model: Jobs,
          attributes: ["title"],
          where: {
            companyId,
            workflowId: {
              [Op.not]: null
            }
          },
          include: [
            {
              model: Workflow,
              attributes: ["title", "workflow"]
            }, {
              model: Company,
              attributes: ["id", "name"]
            }
          ]
        },
        {
          model: User,
          where: whereUser,
          attributes: [
            "id",
            "email",
            "avatar",
            "firstname",
            "middlename",
            "lastname",
            "phone",
          ],
          include: [
            {
              model: Candidate,
              attributes: [
                "id",
                "experience",
                "currentCtc",
                "preferencesExpectedCTC",
                "degree",
                "locationId",
              ],
            },
          ],
        },
        {
          model: Round,
          attributes: ["jobId", "rounds"],
        },
      ],
      distinct: true,
      attributes: [
        "id",
        "jobId",
        "userId",
        "match",
        "saveJob",
        "applyJob",
        "subscribeJob",
        "status",
        "totalScores",
      ],
      order,
      limit: dto.limit,
      offset: dto.offset,
    });
    return data;
  }

  async getSummary(userId: number, jobId: number, companyId: number) {
    return await this.subscribeRepository.findOne({
      where: {
        userId,
        jobId,
      },
      attributes: [
        "id",
        "userId",
        "jobId",
        "match",
        "status",
        "totalScores",
        "summary",
      ],
      include: [
        {
          model: User,
          attributes: [
            "id",
            "email",
            "avatar",
            "firstname",
            "middlename",
            "lastname",
            "phone",
          ],
          include: [
            {
              model: Candidate,
              attributes: [
                "id",
                "experience",
                "currentCtc",
                "preferencesExpectedCTC",
                "degree",
                "locationId",
                "positionId",
              ],
              include: [
                {
                  model: Location,
                  attributes: ["id", "city", "state", "st"],
                },
                {
                  model: Position,
                  attributes: ["id", "value", "label"],
                },
                {
                  model: Industry,
                  attributes: ["id", "value", "label"],
                },
              ],
            },
          ],
        },
        {
          model: Jobs,
          attributes: ["id", "companyId", "title"],
          where: {
            companyId,
          },
        },
      ],
    });
  }

  async changeStatus(dto: EditStatusDto, companyId: number) {
    const data = await this.subscribeRepository.findOne({
      where: {
        id: dto.id,
      },
      include: [
        {
          model: Jobs,
          attributes: ["id", "companyId"],
          where: {
            companyId,
          },
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['email']
            }
          ]
        },
      ],
    });

    // JobTarget tracking: Update applicant status to "Rejected" when subscribe status is changed to "Rejected"
    if (dto.status === "Rejected" && data?.JT_GUID && data?.job?.author?.email) {
      try {
        await this.jobTargetService.updateApplicantStatus(
          data.JT_GUID,
          "Rejected",
          data.job.author.email
        );
        Logger.log(`JobTarget applicant status updated to "Rejected" for GUID: ${data.JT_GUID} via subscribe status change`);
      } catch (error) {
        Logger.error(`Failed to update JobTarget applicant status to "Rejected": ${error.message}`);
      }
    }

    Object.assign(data, { status: dto.status });
    return await data.save();
  }

  async getCandidateScoreBoards(dto: FilterSubscribeDto, userId: number) {
    let order: any[] = [];
    let whereJob: any = {
      workflowId: {
        [Op.not]: null,
      },
      companyId: {
        [Op.not]: null,
      },
    };
    if (dto.searchValue) {
      whereJob.title = {
        [Op.iLike]: `%${dto.searchValue}%`,
      };
    }

    if (dto.sortBy && dto.sortType) {
      order.push([`${dto.sortBy}`, `${dto.sortType}`]);
    }

    const data = await this.subscribeRepository.findAndCountAll({
      where: {
        userId,
        applyJob: true,
        roundId: {
          [Op.not]: null,
        },
      },
      attributes: [
        "id",
        "jobId",
        "userId",
        "applyJob",
        "saveJob",
        "status",
        "totalScores",
      ],
      include: [
        {
          model: Jobs,
          where: whereJob,
          attributes: ["id", "title"],
          include: [
            {
              model: Company,
              attributes: ["id", "name", "avatar"],
            },
            {
              model: Workflow,
              attributes: ["id", "title"],
            },
          ],
        },
        {
          model: Round,
          attributes: ["rounds", "id"],
        },
      ],
      limit: dto.limit,
      offset: dto.offset,
      order,
    });
    return data;
  }

  async countSavedJobs(userId: number) {
    const date = new Date();
    const startOfDay = new Date(date);
    startOfDay.setUTCHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setUTCHours(23, 59, 59, 999);
    const data = await this.subscribeRepository.findAndCountAll({
      where: {
        userId,
        saveJob: true,
        updatedAt: {
          [Op.between]: [startOfDay, endOfDay],
        },
      },
    });
    return { todaySavedJobs: data.count };
  }

  async changeJobstatus(dto: EditJobsStatus, jobId: number, userId: number) {
    const [data] = await this.subscribeRepository.findOrCreate({
      where: {
        userId,
        jobId: jobId,
      },
      include: [{ model: User, attributes: ["id", "firstname", "lastname", "email", "avatar"] }, { model: Jobs, attributes: ["id", "title", "authorId"], include: [Company] }]
    });

    const match = await this.match(userId, jobId);
    Object.assign(data, {
      ...dto,
      userId: data.userId,
      jobId: data.jobId,
      match: Number(match),
    });
    return data.save();
  }


  async getJobSubscribeDetailsByUserId(userId: number, jobId: number) {
    try {
      return await this.subscribeRepository.findOne({
        where: {
          userId,
          jobId
        }
      });
    }
    catch (error) {
      Logger.log("location - SubscibeService/getSubscribeDetails", error);
      throw new InternalServerErrorException({ status: 500, message: "error happened while getting subscribe details by jobId and userId" });
    }
  }

  async startWorkflow(dto: StartWorkflowDto, companyId: number, authHeader) {
    const { jobId, userId, JT_GUID } = dto
    const subscribe = await this.subscribeRepository.findOne({ where: { jobId, userId } })
    const job = await this.jobRepository.findOne({ where: { id: jobId, companyId }, include: [{ model: User, as: 'author' }] })
    const userData = await this.userService.getUserByUserId(userId)
    // Check if workflow is associated with job or not
    if (job.workflowId) {
      const workflowData = await this.workflowService.getOne(
        job.workflowId,
        job.companyId
      );
      subscribe.status = "In Progress";

      // Store JT_GUID if provided
      if (JT_GUID) {
        subscribe.JT_GUID = JT_GUID;
      }
      const round = await this.roundService.create({
        jobId: dto.jobId,
        userId,
        rounds: workflowData?.workflow?.map((item, i) => {
          return {
            title: item.name,
            status: roundStatus.PENDING,
            date: { start: null, end: null },
            interviewers: [],
            score: null,
            comments: "",
          };
        }),
      });
      subscribe.roundId = round.id
      await subscribe.save()

      // JobTarget tracking: Update applicant status to "Application Viewed" when workflow starts
      if (subscribe.JT_GUID && job?.author?.email) {
        try {
          await this.jobTargetService.updateApplicantStatus(
            subscribe.JT_GUID,
            "Application Viewed",
            job.author.email
          );
          Logger.log(`JobTarget applicant status updated for GUID: ${subscribe.JT_GUID}`);
        } catch (error) {
          Logger.error(`Failed to update JobTarget applicant status: ${error.message}`);
        }
      }

      try {

        const id = userId;
        // const url = `https://wtt-dev.urecruits.com/api/assignment/candidate/${id}`;
        const url = `http://localhost:3000/api/assignment/candidate/${id}`;
        const assignment = await this.httpService
          .patch(url, {
            jobId: job?.id,
            workflowId: job?.workflowId,
          }, {
            headers: {
              Authorization: authHeader,
            }
          })
          .toPromise();
        if (job?.isAssessment && assignment.data) {
          await this.userAssignmentsService.create({ userId, assignmentId: assignment.data?.id, assignmentName: assignment.data?.title, hasActiveAssignment: true }, companyId)
        }

      } catch (err) {
        Logger.log("Error creating candidates in assignments:", err);
      }

      return {
        subscribeId: subscribe.id,
        userData,
        job,
        workflowData
      }
    }
  }

  async getAppliedCandidatesByCompany(dto: CandidateFiltersDto, companyId) {
    const appliedUsers = await this.subscribeRepository.findAndCountAll({
      attributes: ["userId", "jobId"],
      include: [{
        model: Jobs,
        attributes: ["id", "title"],
        where: {
          companyId,
          isAssessment: false
        }
      }]
    })

    const userIds = appliedUsers.rows.map(user => user.dataValues.userId);

    if (userIds.length === 0) {
      return { count: 0, rows: [] };
    }
    const candidates = await this.candidateService.getAllCandidates({
      ...dto,
      userId: userIds,
    });
    let users: any[] = appliedUsers.rows.map(user => {
      const candidate = candidates.rows.find(c => c.userId === user.dataValues.userId);
      return candidate ? { job: user.job, ...candidate.toJSON() } : null;
    }).filter(Boolean);

    return { count: users.length, rows: users };
  }
}

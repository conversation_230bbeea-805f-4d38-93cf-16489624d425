{"name": "assessment-api", "version": "0.0.1", "dependencies": {"@nestjs/common": "^8.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.0.0", "@nestjs/core": "^8.0.0", "@nestjs/microservices": "^8.2.4", "@nestjs/websockets": "^8.2.5", "@nestjs/platform-socket.io": "^8.2.5", "redis": "^3", "@nestjs/platform-express": "^8.0.0", "@nestjs/axios": "^3.0.0", "@nestjs/config": "^1.1.6", "undici": "^5.5.1", "sequelize-typescript": "^2.1.5", "sequelize": "^6.25.3", "@nestjs/swagger": "^5.2.0", "swagger-ui-express": "^4.3.0", "@nestjs/sequelize": "^8.0.0", "@langchain/openai": "^0.0.28", "@langchain/core": "^0.3.57", "moment": "^2.29.4", "@sendgrid/mail": "^7.6.0", "nodemailer": "^6.7.2", "aws-sdk": "2.1059.0", "@qdrant/qdrant-js": "^1.13.0", "@google-cloud/talent": "^4.1.1", "langchain": "^0.1.36", "pdf-parse": "^1.1.1", "puppeteer": "^22.6.2", "zod": "^3.23.5", "@convergence/jwt-util": "^0.2.0", "uuid": "^9.0.0", "@nestjs/passport": "^9.0.0", "passport": "^0.6.0", "cross-fetch": "^3.1.5", "passport-jwt": "^4.0.0", "jwks-rsa": "^2.0.5", "dotenv": "^16.4.7", "@temporalio/worker": "^1.11.2", "@temporalio/workflow": "^1.11.2", "@temporalio/client": "^1.11.2"}, "main": "main.js"}
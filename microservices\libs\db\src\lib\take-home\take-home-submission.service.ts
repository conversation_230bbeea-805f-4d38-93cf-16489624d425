import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { TakeHomeSubmission } from './take-home-submission.model';
import { TakeHomeTask } from './take-home-task.model';
import { Questions } from '../questions/questions.model';
import { TestCase } from '../testcases/testcase.model';
import { Op } from 'sequelize';

export interface CreateSubmissionDto {
  jobId: number;
  candidateId: number;
  assessmentId: number;
  assignmentId?: number;
  duration?: string;
  language?: string;
  questionSubmissions: any[];
  testCaseSummary?: any;
  submittedAt?: Date;
}

export interface UpdateSubmissionDto {
  totalScore?: number;
  feedback?: string;
  testCaseSummary?: any;
}

@Injectable()
export class TakeHomeSubmissionService {
  constructor(
    @InjectModel(TakeHomeSubmission)
    private takeHomeSubmissionModel: typeof TakeHomeSubmission,
  ) {}

  async create(dto: CreateSubmissionDto): Promise<TakeHomeSubmission> {
    try {
      console.log('=== TAKE-HOME SUBMISSION SERVICE ===');
      console.log('Creating submission with data:', JSON.stringify(dto, null, 2));

      // Check if submission already exists for this candidate and assessment
      console.log('Checking for existing submission...');
      const existingSubmission = await this.takeHomeSubmissionModel.findOne({
        where: {
          candidateId: dto.candidateId,
          assessmentId: dto.assessmentId,
          jobId: dto.jobId,
        },
      });

      console.log('Existing submission found:', existingSubmission);

      if (existingSubmission) {
        console.log('Submission already exists, throwing conflict exception');
        throw new HttpException(
          'Submission already exists for this assessment',
          HttpStatus.CONFLICT,
        );
      }

      // Calculate total score from question submissions
      const totalScore = this.calculateTotalScore(dto.questionSubmissions);
      console.log('Calculated total score:', totalScore);

      console.log('Creating submission in database...');
      const submission = await this.takeHomeSubmissionModel.create({
        ...dto,
        totalScore,
        submittedAt: dto.submittedAt || new Date(),
      });

      console.log('Submission created successfully:', submission.toJSON());
      return submission;
    } catch (error) {
      console.error('Error creating take-home submission:', error);
      Logger.error('Error creating take-home submission:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to create submission',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findById(id: number): Promise<TakeHomeSubmission> {
    const submission = await this.takeHomeSubmissionModel.findByPk(id, {
      include: [
        {
          model: TakeHomeTask,
          as: 'assessment',
          include: [
            {
              model: Questions,
              as: 'questions',
              include: [
                {
                  model: TestCase,
                  as: 'testcases',
                  required: false,
                },
              ],
            },
          ],
        },
      ],
    });

    if (!submission) {
      throw new HttpException('Submission not found', HttpStatus.NOT_FOUND);
    }

    return submission;
  }

  async findByJobAndCandidate(
    jobId: number,
    candidateId: number,
  ): Promise<TakeHomeSubmission[]> {
    return this.takeHomeSubmissionModel.findAll({
      where: { jobId, candidateId },
      include: [
        {
          model: TakeHomeTask,
          as: 'assessment',
          include: [
            {
              model: Questions,
              as: 'questions',
              required: false,
            },
          ],
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  async findByCandidateAndJob(
    candidateId: number,
    jobId: number,
  ): Promise<TakeHomeSubmission> {
    const submission = await this.takeHomeSubmissionModel.findOne({
      where: { candidateId, jobId },
      include: [
        {
          model: TakeHomeTask,
          as: 'assessment',
          include: [
            {
              model: Questions,
              as: 'questions',
              required: false,
            },
          ],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return submission;
  }

  async findByAssessment(assessmentId: number): Promise<TakeHomeSubmission[]> {
    return this.takeHomeSubmissionModel.findAll({
      where: { assessmentId },
      include: [
        {
          model: TakeHomeTask,
          as: 'assessment',
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  async findForReview(
    companyId: number,
    filters: {
      limit?: number;
      offset?: number;
      search?: string;
      jobId?: number;
      assessmentId?: number;
    } = {},
  ): Promise<{ rows: TakeHomeSubmission[]; count: number }> {
    const whereClause: any = {};
    
    if (filters.jobId) {
      whereClause.jobId = filters.jobId;
    }
    
    if (filters.assessmentId) {
      whereClause.assessmentId = filters.assessmentId;
    }

    const includeClause = [
      {
        model: TakeHomeTask,
        as: 'assessment',
        where: { companyId },
        include: [
          {
            model: Questions,
            as: 'questions',
            required: false,
          },
        ],
      },
    ];

    const { rows, count } = await this.takeHomeSubmissionModel.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: filters.limit || 10,
      offset: filters.offset || 0,
      order: [['createdAt', 'DESC']],
      distinct: true,
    });

    return { rows, count };
  }

  async update(id: number, dto: UpdateSubmissionDto): Promise<TakeHomeSubmission> {
    const submission = await this.findById(id);
    
    await submission.update(dto);
    
    return submission;
  }

  async delete(id: number): Promise<void> {
    const submission = await this.findById(id);
    await submission.destroy();
  }

  private calculateTotalScore(questionSubmissions: any[]): number {
    if (!questionSubmissions || questionSubmissions.length === 0) {
      return 0;
    }

    return questionSubmissions.reduce((total, submission) => {
      const score = submission.score || 0;
      return total + score;
    }, 0);
  }

  async getSubmissionStats(assessmentId: number): Promise<{
    totalSubmissions: number;
    averageScore: number;
    passRate: number;
  }> {
    const submissions = await this.takeHomeSubmissionModel.findAll({
      where: { assessmentId },
      attributes: ['totalScore'],
    });

    const totalSubmissions = submissions.length;
    
    if (totalSubmissions === 0) {
      return {
        totalSubmissions: 0,
        averageScore: 0,
        passRate: 0,
      };
    }

    const totalScore = submissions.reduce((sum, sub) => sum + (sub.totalScore || 0), 0);
    const averageScore = totalScore / totalSubmissions;
    
    // Assuming pass threshold is 60% - this could be configurable
    const passThreshold = 60;
    const passedSubmissions = submissions.filter(sub => (sub.totalScore || 0) >= passThreshold).length;
    const passRate = (passedSubmissions / totalSubmissions) * 100;

    return {
      totalSubmissions,
      averageScore: Math.round(averageScore * 100) / 100,
      passRate: Math.round(passRate * 100) / 100,
    };
  }
}

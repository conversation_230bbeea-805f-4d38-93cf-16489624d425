{"name": "pre-recruitment-api", "version": "0.0.1", "dependencies": {"@nestjs/common": "^8.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.0.0", "@nestjs/microservices": "^8.2.4", "@nestjs/core": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/websockets": "^8.2.5", "@nestjs/platform-socket.io": "^8.2.5", "redis": "^3", "sequelize-typescript": "^2.1.5", "sequelize": "^6.25.3", "@nestjs/swagger": "^5.2.0", "swagger-ui-express": "^4.3.0", "@nestjs/sequelize": "^8.0.0", "@nestjs/axios": "^3.0.0", "@nestjs/config": "^1.1.6", "@sendgrid/mail": "^7.6.0", "nodemailer": "^6.7.2", "@temporalio/worker": "^1.11.2", "@temporalio/workflow": "^1.11.2", "@temporalio/client": "^1.11.2", "@nestjs/passport": "^9.0.0", "passport": "^0.6.0", "cross-fetch": "^3.1.5", "passport-jwt": "^4.0.0", "jwks-rsa": "^2.0.5", "dotenv": "^16.4.7", "@google-cloud/talent": "^4.1.1", "langchain": "^0.1.36", "pdf-parse": "^1.1.1", "puppeteer": "^22.6.2", "zod": "^3.23.5", "@langchain/openai": "^0.0.28", "uuid": "^9.0.0", "bcrypt": "^5.0.1", "googleapis": "^107.0.0", "@dropbox/sign": "^1.3.0", "@microsoft/microsoft-graph-client": "^3.0.4", "isomorphic-fetch": "^3.0.0", "@qdrant/qdrant-js": "^1.13.0", "aws-sdk": "2.1059.0", "firebase-admin": "^12.1.1", "nestjs-stripe": "1.0.0", "stripe": "^8.222.0", "@temporalio/common": "^1.11.2", "@langchain/core": "^0.3.57", "moment": "^2.29.4", "@convergence/jwt-util": "^0.2.0", "undici": "^5.5.1", "@azure/msal-node": "3.6.2", "twilio": "^3.81.0", "@opensearch-project/opensearch": "^2.5.0", "multer": "1.4.4", "multer-s3": "2.10.0", "@langchain/langgraph": "^0.3.0"}, "main": "main.js"}
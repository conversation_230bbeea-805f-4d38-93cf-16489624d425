{"version": "5.0", "deps": {"country-state-city": "^3.2.1", "@nrwl/workspace": "13.4.3", "@nrwl/cli": "13.4.3", "@nrwl/tao": "13.4.3", "typescript": "4.2.4"}, "pathMappings": {}, "nxJsonPlugins": [], "nodes": {"coding-assessments": {"name": "coding-assessments", "type": "lib", "data": {"root": "apps/assessment/coding-assessments", "sourceRoot": "apps/assessment/coding-assessments", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "prepare": {"executor": "@nrwl/workspace:run-script", "options": {"script": "prepare"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/assessment/coding-assessments/.eslintrc", "hash": "1bf74d3742057ffc653ed4962f71b389e6155e60"}, {"file": "apps/assessment/coding-assessments/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/assessment/coding-assessments/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/assessment/coding-assessments/api/api.ts", "hash": "2259970688de17da8ad2448b1800fb9bf523dadc"}, {"file": "apps/assessment/coding-assessments/api/fake.api/user.api.js", "hash": "8e710c5f602e8cd7f95eb4d0c3bcee32add9b5a6"}, {"file": "apps/assessment/coding-assessments/api/index.js", "hash": "c044671c11c1bf2d296b9759411dafe7fc28de6b"}, {"file": "apps/assessment/coding-assessments/api/user2.api.js", "hash": "b53b8b101cf2fe5f4c768dac08a29daec1a07532"}, {"file": "apps/assessment/coding-assessments/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/assessment/coding-assessments/components/_elements/CodingSelect.tsx", "hash": "714f740d300c4c95fb790da7fe961ff710ea3196"}, {"file": "apps/assessment/coding-assessments/components/ArrayInputs.tsx", "hash": "9ddc8b440a3f593f6d94b6c8ce4e68e82547005d"}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/ActionProvider.tsx", "hash": "6c1c62a2bf2f6165d06b4a8e0fc23750699bb3da", "deps": ["api"]}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/actionUtils.ts", "hash": "6d60575694e230c963d847c591ed2142bcd36052", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/AssessmentCreationChatbot.tsx", "hash": "9761d19c962797aeb934cee7833e3dd5e0a48872"}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/config.ts", "hash": "e2ab5479ab467e44b5b13ebc17579d7545f842ee"}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/CustomComponents/BotAvatar.tsx", "hash": "64ca379fea4a274f234472241af76ccf4d6fbfd0"}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/CustomComponents/ChatHeader.tsx", "hash": "298eafa27311820fe5fbed979657b282272825d6"}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/CustomComponents/QuestionCards.tsx", "hash": "e4f18690ec70737071a84c85ce44f5e0a724f197"}, {"file": "apps/assessment/coding-assessments/components/AssessmentChatbot/MessageParser.tsx", "hash": "e4421dc088e6eb6b4161e670a3ea09602b013d8f"}, {"file": "apps/assessment/coding-assessments/components/CheckboxButton.tsx", "hash": "32ea7d64995f25876f735498a86f58a6be0f9e3e"}, {"file": "apps/assessment/coding-assessments/components/CodingAssessments.tsx", "hash": "cdcd064c99f40eddfd075c26e8dd5babacf83e2b", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/assessment/coding-assessments/components/CodingAssessmentsFilters.tsx", "hash": "89300329670f720df2039fe609df6851b403f46f"}, {"file": "apps/assessment/coding-assessments/components/Dialog.tsx", "hash": "1109486ef5ca6af6251080e5e5eb04bb7a6b9ed0"}, {"file": "apps/assessment/coding-assessments/components/Dropdown.tsx", "hash": "3b188412d15352f4ad6c37bb21941d4f8f06a446"}, {"file": "apps/assessment/coding-assessments/components/filters/TabCreatedBy.tsx", "hash": "08a1e387c2031fdf5eb787cf7737c8d3324adcfb"}, {"file": "apps/assessment/coding-assessments/components/filters/TabCreatedOn.tsx", "hash": "3fe3e6010732d92171eb9fec3e5ee7be1ee89423"}, {"file": "apps/assessment/coding-assessments/components/filters/TabLanguages.tsx", "hash": "deda09043cd4ca943ac8326795725aca519627f7"}, {"file": "apps/assessment/coding-assessments/components/filters/TabPackages.tsx", "hash": "0e34e51a013c34030dd5b1019c01e7d496445052"}, {"file": "apps/assessment/coding-assessments/components/filters/TabQuestionType.tsx", "hash": "3fe806ab862598164eb0bfae3f4d331144be847b"}, {"file": "apps/assessment/coding-assessments/components/Loader.tsx", "hash": "02fab1cc7ae47457806ef880ea3607edd6c28e71"}, {"file": "apps/assessment/coding-assessments/components/SearchInput.tsx", "hash": "24ff88319121f32525bb1c23c5f40b459a09ac74"}, {"file": "apps/assessment/coding-assessments/components/SmallLoader.tsx", "hash": "f439077c82e7770d2d860e818461267a06250c44"}, {"file": "apps/assessment/coding-assessments/components/tasks/AddQuestionTask.tsx", "hash": "5c306adfd0f0fca2075d052db8965407679fece0"}, {"file": "apps/assessment/coding-assessments/components/tasks/DialogCreate.tsx", "hash": "b21655467fed18e1639aa9e8e1b6e4828cf38826"}, {"file": "apps/assessment/coding-assessments/components/tasks/DialogDatabase.tsx", "hash": "560f146efb3d937c693e0c7fd6df558b735ce908", "deps": ["api"]}, {"file": "apps/assessment/coding-assessments/components/tasks/DialogDeleteQuestion.tsx", "hash": "dd4a07dfd8fd496822ec37559212d96cdffd46b4", "deps": ["api"]}, {"file": "apps/assessment/coding-assessments/components/tasks/DialogDraftQuestion.tsx", "hash": "5c6a66ebde9221518fd7aaab0952ad2544a8f3c8"}, {"file": "apps/assessment/coding-assessments/components/tasks/DialogLiveDatabase.tsx", "hash": "4b39a5f9f830a96d69996eb9d36054c272e93c34", "deps": ["api"]}, {"file": "apps/assessment/coding-assessments/components/tasks/DialogQuestion.tsx", "hash": "e01561bf057483ac3b00a2c163af438c4c85b608", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/components/tasks/DialogSuccessSaveDraft.tsx", "hash": "2ff856342fd68e27e51b5ada912d824bd21b41e3"}, {"file": "apps/assessment/coding-assessments/components/tasks/HomeTask.tsx", "hash": "1b9eb716bc303c86c6b17b91d15e10493bf93114", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/components/tasks/HomeTaskStepGeneral.tsx", "hash": "6ee8f38e99f8c41a92b9bf2f6b8c72231ac6c73c"}, {"file": "apps/assessment/coding-assessments/components/tasks/HomeTaskStepOne.tsx", "hash": "53ff31e4831d75134790f7a27e8a906090165049", "deps": ["api"]}, {"file": "apps/assessment/coding-assessments/components/tasks/HomeTaskStepThree.tsx", "hash": "e745b90f4be11a29f7f4cbce9c3fe28fa1b1517b"}, {"file": "apps/assessment/coding-assessments/components/tasks/HomeTaskStepTwo.tsx", "hash": "5eee0568720d09a7556fb4993455366c907cb3de", "deps": ["api"]}, {"file": "apps/assessment/coding-assessments/components/tasks/LiveTask.tsx", "hash": "649a7f33fa62e83cb8e2d1ae9b87644156272eba", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/components/tasks/LiveTaskNextStep.tsx", "hash": "3346bb55f4f69c05d95a7ef02c7adcd69c335d33"}, {"file": "apps/assessment/coding-assessments/components/tasks/NavigateToLiveTask.tsx", "hash": "735f9cfd542fb28dde13b1e48783d0e47be40b39"}, {"file": "apps/assessment/coding-assessments/components/tasks/NavigateToSteps.tsx", "hash": "2b3f06f40fd11ce0db3da8873174854f8fd6f228"}, {"file": "apps/assessment/coding-assessments/components/tasks/ProgressBar.tsx", "hash": "ee71f7877fad95fcf7f586f6aa27f0c91c451245"}, {"file": "apps/assessment/coding-assessments/components/tasks/ReachEditor.tsx", "hash": "bf02e90669d55676c4b06258ce8d0a4d8fa3b429"}, {"file": "apps/assessment/coding-assessments/config/store.ts", "hash": "da6ef426400505b53a706cef725f1698146cadd2"}, {"file": "apps/assessment/coding-assessments/config/utils.ts", "hash": "7e150455effc34657d3707f93766a530754b685c"}, {"file": "apps/assessment/coding-assessments/enums/CodingAssessmentEnums.ts", "hash": "6ba810fd7502f0163f38d52f10c0ad88393c06ea"}, {"file": "apps/assessment/coding-assessments/hook/common/use-outside-click.ts", "hash": "0c660d2ab34b8be598adac493b943cefa58420ba"}, {"file": "apps/assessment/coding-assessments/hook/common/useWindowSize.ts", "hash": "7c3e1b7eef956c6f9e0ade07c3f49ade0fef8461"}, {"file": "apps/assessment/coding-assessments/hook/deleteData.ts", "hash": "a4f69d1a223923d2ff17c4d3378ab75ac7181ab1"}, {"file": "apps/assessment/coding-assessments/hook/fetchData.tsx", "hash": "85c533c5ebd84e7f3def60b69890bc0b46c40fd4", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/hook/patchData.ts", "hash": "d769fd72fafed13b89695e04387a615db4b1b657", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/hook/portal.tsx", "hash": "39ce7351f57553af0405c71a61f12ab2527b2270"}, {"file": "apps/assessment/coding-assessments/hook/postData.ts", "hash": "2fc1a2e0a7cabc42ff5dabe15c060c8d87f6d104", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/hook/putData.tsx", "hash": "ade7e22c2dbc928e6e18027ac40f29d17fc16948"}, {"file": "apps/assessment/coding-assessments/hook/SearchPortal.tsx", "hash": "2e2089eebab9c3cfd6417b3a668d982709b940f8"}, {"file": "apps/assessment/coding-assessments/hook/validateUrl.ts", "hash": "c2891576e7fcc592b320fc08a42df3bbed3d0dee"}, {"file": "apps/assessment/coding-assessments/images/addQuestionTestCase.svg", "hash": "049fbd5e0d415f34154c532984cd21bfc69fe25b"}, {"file": "apps/assessment/coding-assessments/images/arrowTask.svg", "hash": "a85e45f09f98d4ee70c422add631e7a721c444a5"}, {"file": "apps/assessment/coding-assessments/images/backToGeneralArrow.svg", "hash": "4137dccd1a5db80c2dfc6b27666625e9a7bd22d1"}, {"file": "apps/assessment/coding-assessments/images/burgerIcon.svg", "hash": "f53e6d0d59c177e91f3845363845af281d80f84a"}, {"file": "apps/assessment/coding-assessments/images/circleCheck.svg", "hash": "****************************************"}, {"file": "apps/assessment/coding-assessments/images/consoleErrorIcon.svg", "hash": "5df0306cae6fb81453eda5e01f04dbeb15df6366"}, {"file": "apps/assessment/coding-assessments/images/crossQuestion.svg", "hash": "1193b83f6205d16873759ebcb9170b209d353ad3"}, {"file": "apps/assessment/coding-assessments/images/crossQuestionWhite.svg", "hash": "17313cb6b377e6cda60d23668ccde03ba4f13517"}, {"file": "apps/assessment/coding-assessments/images/delete.svg", "hash": "08a3e44b0bb40295fbb24b30858ba53e8b2fe6e7"}, {"file": "apps/assessment/coding-assessments/images/deleteInputTask.svg", "hash": "f3a5871d2d58bc91bf0a6ad9fbda7541630e7e30"}, {"file": "apps/assessment/coding-assessments/images/DeleteQuestion.svg", "hash": "a33a4d06c8fca5e365ff9d6e0e332b645250d43b"}, {"file": "apps/assessment/coding-assessments/images/dialogCreate.svg", "hash": "eb1cc990743823308d05d8780af3cda1e671cb2b"}, {"file": "apps/assessment/coding-assessments/images/dropDownIconCommon.svg", "hash": "4fdf0f34a073f9ccbc10465f738d09a7c2b03c67"}, {"file": "apps/assessment/coding-assessments/images/dropDownSelected.svg", "hash": "b809459cda9e3712ed42723eef481ecba49dfa31"}, {"file": "apps/assessment/coding-assessments/images/filterSm.svg", "hash": "f3fb033e65109212cdce024673655f56d2c2a33c"}, {"file": "apps/assessment/coding-assessments/images/hideCellIco.svg", "hash": "612f0678c6e23c29dd688287beb273edfd1c5cb8"}, {"file": "apps/assessment/coding-assessments/images/icon/approve_job_ic.svg", "hash": "88019e070c7a267a3e16aa7b6eb166179269804c"}, {"file": "apps/assessment/coding-assessments/images/icon/arrow.svg", "hash": "fbeef4dbbe69d50dfe86cd447668c7c919497712"}, {"file": "apps/assessment/coding-assessments/images/icon/calendar_ic.svg", "hash": "81d2b1b2c84fec48e199af69f7db0e23ea9a50bb"}, {"file": "apps/assessment/coding-assessments/images/icon/cancel.svg", "hash": "01cd5ccb386860167e9a05af94395567c0d3ad5a"}, {"file": "apps/assessment/coding-assessments/images/icon/chatbot.svg", "hash": "26486efcddd6ce537e90fde0c0efb28f2a043402"}, {"file": "apps/assessment/coding-assessments/images/icon/close.svg", "hash": "ce25366d951f523c6f07d37840246aae8af924c2"}, {"file": "apps/assessment/coding-assessments/images/icon/cross.svg", "hash": "29eb241f176250b11532e4723b8bcd181195a954"}, {"file": "apps/assessment/coding-assessments/images/icon/delete_ic.svg", "hash": "9ca29516d488f6e4ebdb1a717a11938013003421"}, {"file": "apps/assessment/coding-assessments/images/icon/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/assessment/coding-assessments/images/icon/edit_ic.svg", "hash": "5f3b472b990252dbffb690826248bda2045ef414"}, {"file": "apps/assessment/coding-assessments/images/icon/empty.svg", "hash": "aa2d2cc953da682d3f0c9cbaaa6f0a6811a90b3e"}, {"file": "apps/assessment/coding-assessments/images/icon/remove.svg", "hash": "746d1ad7cba2c65ad9d9da725304e2bb67dc3128"}, {"file": "apps/assessment/coding-assessments/images/iconCalendar.svg", "hash": "1b1ce61f4b97b5a4c326abdacf8949207720d73b"}, {"file": "apps/assessment/coding-assessments/images/iconCreateHomeTask.svg", "hash": "2562f4e67f97362fe55a1dd31c118deb5c590be3"}, {"file": "apps/assessment/coding-assessments/images/iconCreateLiveTask.svg", "hash": "623d0bcf1110d21da7dd542afda44648db2436d8"}, {"file": "apps/assessment/coding-assessments/images/iconSearchGray.svg", "hash": "f76bb215bea110038c01a677bbd91ab5dc8aa5c3"}, {"file": "apps/assessment/coding-assessments/images/itemNextLabel.svg", "hash": "10f408734b877877c48d50a061e9e2aaf0cb0bb5"}, {"file": "apps/assessment/coding-assessments/images/itemPrevLabel.svg", "hash": "cb3a23e4a04b65873f67042fd3ca6578a66ac2a8"}, {"file": "apps/assessment/coding-assessments/images/pencil.svg", "hash": "234616591558bc48512c4dbc439c39f0d4a24e4c"}, {"file": "apps/assessment/coding-assessments/images/resetButton.svg", "hash": "c68fe1318cce6db6c35fdafe2ec05ccabc7c027b"}, {"file": "apps/assessment/coding-assessments/images/saveFilterButton.svg", "hash": "e44949cc24c5e4fa01b759689c5d31c2c8283e13"}, {"file": "apps/assessment/coding-assessments/images/search.svg", "hash": "a5d046a0d471e5c0f67d334494863fba404093b6"}, {"file": "apps/assessment/coding-assessments/images/sendToDraft.svg", "hash": "438b80cc3e8479059d5d63c18f07b740b73be54d"}, {"file": "apps/assessment/coding-assessments/images/settings.svg", "hash": "8e2370bb3bf2cfc6813319a39a2c52076aa22f71"}, {"file": "apps/assessment/coding-assessments/images/showCellIco.svg", "hash": "bd90ad3f313d9c708cfa3c65fd3e89fc2a82975f"}, {"file": "apps/assessment/coding-assessments/images/split-icon-dark.svg", "hash": "abf31908867dfd931eda50262e3cc8dadf972f52"}, {"file": "apps/assessment/coding-assessments/images/split-icon.svg", "hash": "7a3468ddef964b900f6834b8b6e8ee631bb171ee"}, {"file": "apps/assessment/coding-assessments/images/successSaveDraft.svg", "hash": "89b20381cb976b2156d113de94796f9569c9e06e"}, {"file": "apps/assessment/coding-assessments/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/assessment/coding-assessments/package.json", "hash": "f89dd3fa1acf7e9b0b630ddd6033336acec39c3b", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/assessment/coding-assessments/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/assessment/coding-assessments/src/root.component.tsx", "hash": "1d4bc7bf4d9cdc25bc1566b47c8c5adbfe20e7e6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/src/urecruits-coding-assessments.tsx", "hash": "23e936c0a9916f30e17053d6e8b5a5a7f0c1d4f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/coding-assessments/store/assessmentTask/assessmentTask.actions.ts", "hash": "0ccf97106f8804b66344b856ad39e73eec13c405"}, {"file": "apps/assessment/coding-assessments/store/assessmentTask/assessmentTask.reducer.ts", "hash": "3cbd9ffd1f20903510149f74722374e844146cd1"}, {"file": "apps/assessment/coding-assessments/store/assessmentTask/assessmentTask.selectors.ts", "hash": "9e0be791df311334ae50e8fdf42cd729d23b09df"}, {"file": "apps/assessment/coding-assessments/store/chatbot/chatAssessmentReducer.ts", "hash": "0314328100caef0ec1626d359564a479809bb1d1"}, {"file": "apps/assessment/coding-assessments/store/coding-assessment/codingAssessmentsTableReducer.tsx", "hash": "1b63b7d6ba8fffdbac100509822f5e73607a2411"}, {"file": "apps/assessment/coding-assessments/store/filter/filter.actions.ts", "hash": "69565fec86df774db43eec1232516779809922c1"}, {"file": "apps/assessment/coding-assessments/store/filter/filter.reducer.ts", "hash": "b162db0b70112671b419649007315efd7b96b682"}, {"file": "apps/assessment/coding-assessments/store/filter/filter.selectors.ts", "hash": "1fb1467425d175ad5a264e8801c22bd80ef3a183"}, {"file": "apps/assessment/coding-assessments/store/index.ts", "hash": "5123d84890f28a286ce08fbf9c9aba13d9b17441"}, {"file": "apps/assessment/coding-assessments/store/liveTask/liveTask.actions.ts", "hash": "ffe698870f72780649c672a7ff3f9ac0fb1ce53a"}, {"file": "apps/assessment/coding-assessments/store/liveTask/liveTask.reducer.ts", "hash": "7a12a1f34e7812c698ded11013abc02f57372add"}, {"file": "apps/assessment/coding-assessments/store/liveTask/liveTask.selectors.ts", "hash": "895605bb9363f468c294cb51be0418e4736723bf"}, {"file": "apps/assessment/coding-assessments/store/root-reducer.ts", "hash": "54c2bf596aca2542727b1bc54f56c1414a94dfc4"}, {"file": "apps/assessment/coding-assessments/styles/_button.scss", "hash": "f4c8d1e6c7255da64f4192e4c0185274d43670bb"}, {"file": "apps/assessment/coding-assessments/styles/_color-palette.scss", "hash": "e3919cb57c46557b9c519eb8a14136f3f4cd483b"}, {"file": "apps/assessment/coding-assessments/styles/_config.scss", "hash": "3f60b8a9c2cf3ef2bb1b436ae69a7ea81ddb4dbf"}, {"file": "apps/assessment/coding-assessments/styles/_loader.scss", "hash": "d0a239563c630323df5f128ee3f77ffd283a624c"}, {"file": "apps/assessment/coding-assessments/styles/_mixins.scss", "hash": "9d6126f29af9ac9a20a5b96064e0328d9f7a1118"}, {"file": "apps/assessment/coding-assessments/styles/_small-loader.scss", "hash": "3345061e3f2da801e9639dc3fb8d3f2ff64ae10f"}, {"file": "apps/assessment/coding-assessments/styles/components/_elements/costomSelect.scss", "hash": "5e093a53da19a0fb6ff7ab4ef5d1e32a6d49dc70"}, {"file": "apps/assessment/coding-assessments/styles/components/_elements/selectFilterStyle.ts", "hash": "7bb4a06ae102363a89b8e4e99b49f35d7557d0b3"}, {"file": "apps/assessment/coding-assessments/styles/components/btn-add.scss", "hash": "7872a1dae945647e312f3753a081fe54d0089b8c"}, {"file": "apps/assessment/coding-assessments/styles/components/btn-tab.scss", "hash": "04fa148f3b2de56d71d7b9ebba4756b27c4dc24b"}, {"file": "apps/assessment/coding-assessments/styles/components/checkbox.scss", "hash": "901c907e3b78ab37f771f3ebad1910cd96771da1"}, {"file": "apps/assessment/coding-assessments/styles/components/coding-assessment-table.scss", "hash": "8edb864534b3b01b853391c2ad5bedcdab7071f3"}, {"file": "apps/assessment/coding-assessments/styles/components/coding-assessments.scss", "hash": "460de1e863a6e6768fca751c8fdcd27559be59af"}, {"file": "apps/assessment/coding-assessments/styles/components/dialog-database.scss", "hash": "83842d7d5a4f0a9c6155ce5615ba70859228a871"}, {"file": "apps/assessment/coding-assessments/styles/components/dialog-draft-question.scss", "hash": "86aebe012480bb04df6960223e49dcbc9c49ee26"}, {"file": "apps/assessment/coding-assessments/styles/components/dialog-question.scss", "hash": "524c8a5a789a28c48baaffe6fd38d5e19dd8b8eb"}, {"file": "apps/assessment/coding-assessments/styles/components/dialog-success.scss", "hash": "81724e52f2163c3b85bf171d72ca9541bfbdbf43"}, {"file": "apps/assessment/coding-assessments/styles/components/dialog.scss", "hash": "3852ef121d85fa985e19ef727c984dc999a3f59f"}, {"file": "apps/assessment/coding-assessments/styles/components/editor-style.scss", "hash": "a5ff00240ce315dcd5d283658592544cebc84c56"}, {"file": "apps/assessment/coding-assessments/styles/components/empty.scss", "hash": "945cac1c97731a899a30295308194f01fd9103d5"}, {"file": "apps/assessment/coding-assessments/styles/components/header.scss", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/assessment/coding-assessments/styles/components/home-task.scss", "hash": "43ae27921b321b23a4cadb779491de33a2056665"}, {"file": "apps/assessment/coding-assessments/styles/components/live-task.scss", "hash": "125a49dda59f97917f016b23d37bf2c79f930609"}, {"file": "apps/assessment/coding-assessments/styles/components/menu-filter-dropdown.scss", "hash": "b54b6928c48c401742c463fe9dfd6ccdd0754561"}, {"file": "apps/assessment/coding-assessments/styles/components/mobile-table-rows.scss", "hash": "72a8e780667d3f2e8387d73b2c043d26ce7455a0"}, {"file": "apps/assessment/coding-assessments/styles/components/search-input.scss", "hash": "d8fddb19d836db706b678ec5168a2bd103ba522e"}, {"file": "apps/assessment/coding-assessments/styles/components/table.scss", "hash": "bca7eec5d0fb3930e457adad15a6dd2852ffb5d1"}, {"file": "apps/assessment/coding-assessments/styles/components/top-field.scss", "hash": "41c12ed036f949b93cc0af17facd242c762b7f88"}, {"file": "apps/assessment/coding-assessments/styles/index.scss", "hash": "2b4db4cb37437aacd4bf26223a3da1aa20a03dd4"}, {"file": "apps/assessment/coding-assessments/styles/main.scss", "hash": "f95458b8249205ce92e2fb1d0aebffcc14879f26"}, {"file": "apps/assessment/coding-assessments/styles/selectCustomStyle.ts", "hash": "342b65b8a9249338c0138b3cb52252998c1391e4"}, {"file": "apps/assessment/coding-assessments/tsconfig.json", "hash": "364ce3d1b4c053c24fc7e4b76c1b5e36369ad0a8"}, {"file": "apps/assessment/coding-assessments/types/assessmentTask/actions.types.ts", "hash": "ea547463e3224a7bbe7d3371fa1a07587b66504e"}, {"file": "apps/assessment/coding-assessments/types/assessmentTask/data.types.ts", "hash": "b53eec25360defa37e5de62c353b976462f323c1"}, {"file": "apps/assessment/coding-assessments/types/assessmentTask/index.ts", "hash": "5015b8ee5ec0464a09f3b3adb5a85afd4fc92d1f"}, {"file": "apps/assessment/coding-assessments/types/coding-assessment-table-types.ts", "hash": "31c5ced0ffa68f2610dc59013fd6da9480cea7b0"}, {"file": "apps/assessment/coding-assessments/types/filter/actions.types.ts", "hash": "829886789a5aee42bb6e24d0beae0599e0a4a071"}, {"file": "apps/assessment/coding-assessments/types/filter/data.types.ts", "hash": "3815785ebea70ac9beb235990cfdc8c9392ddcc0"}, {"file": "apps/assessment/coding-assessments/types/filter/index.ts", "hash": "5015b8ee5ec0464a09f3b3adb5a85afd4fc92d1f"}, {"file": "apps/assessment/coding-assessments/types/index.ts", "hash": "1d476ea8d524b6d4d57494219d049c931fed7faf"}, {"file": "apps/assessment/coding-assessments/types/liveTask/actions.types.ts", "hash": "e35cae3bde9273d68d2843f7f9611d5045594bbb"}, {"file": "apps/assessment/coding-assessments/types/liveTask/data.types.ts", "hash": "5d71c1df933f7b42f2394d79cf2d8c77c25f2766"}, {"file": "apps/assessment/coding-assessments/types/liveTask/index.ts", "hash": "5015b8ee5ec0464a09f3b3adb5a85afd4fc92d1f"}, {"file": "apps/assessment/coding-assessments/utils/testcase/ArrayInputs/arrayInputs.ts", "hash": "bafb0fa7237a547f411dc6462066ea1bbe63385f"}, {"file": "apps/assessment/coding-assessments/utils/testcase/ArrayInputs/type.ts", "hash": "55ca55efdbd0d423772683d0dde0709744337038"}, {"file": "apps/assessment/coding-assessments/utils/testcase/index.ts", "hash": "38e9553ea00ccc31d09c78fb909b8d2364ccafdf"}, {"file": "apps/assessment/coding-assessments/webpack.config.js", "hash": "f8fbc7ba15d242a0b78537c40efb38725eddd037"}]}}, "manage-assignment": {"name": "manage-assignment", "type": "lib", "data": {"root": "apps/assignment/manage-assignment", "sourceRoot": "apps/assignment/manage-assignment", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "prepare": {"executor": "@nrwl/workspace:run-script", "options": {"script": "prepare"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/assignment/manage-assignment/.eslintrc", "hash": "1bf74d3742057ffc653ed4962f71b389e6155e60"}, {"file": "apps/assignment/manage-assignment/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/assignment/manage-assignment/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/assignment/manage-assignment/api/index.js", "hash": "c044671c11c1bf2d296b9759411dafe7fc28de6b"}, {"file": "apps/assignment/manage-assignment/api/optionsSelect.ts", "hash": "c79996335d187cb2ef5b8da825ae79be14fe917b"}, {"file": "apps/assignment/manage-assignment/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/assignment/manage-assignment/components/_elements/CodingSelect.tsx", "hash": "a61e6ff900c29bf2a6063e687cd1d62e61989ef0"}, {"file": "apps/assignment/manage-assignment/components/CheckboxButton.tsx", "hash": "32ea7d64995f25876f735498a86f58a6be0f9e3e"}, {"file": "apps/assignment/manage-assignment/components/CodingAssessments.tsx", "hash": "16b3a4e28abad40b2c88b8cbbe3b8a92890b30fa"}, {"file": "apps/assignment/manage-assignment/components/Dialog.tsx", "hash": "914c8f5f56b8d46a440485013026b0ee81cc47df"}, {"file": "apps/assignment/manage-assignment/components/Dropdown.tsx", "hash": "bb8e4fa4af01acb5aa1840033526560bd5aaba74"}, {"file": "apps/assignment/manage-assignment/components/Loader.tsx", "hash": "b10b2295eafdf90075e4b905f6bdae9b802a27f2"}, {"file": "apps/assignment/manage-assignment/components/Modal.tsx", "hash": "13c4a5911c6eb796449e6bbc096d54426f9be4bf", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-assignment/components/MultySelect.tsx", "hash": "2057745917da3550c49fb1b87ecc41f00384815d"}, {"file": "apps/assignment/manage-assignment/components/SearchInput.tsx", "hash": "3c6ebec84df9f1a87ffc1c727fae99115e007091"}, {"file": "apps/assignment/manage-assignment/components/SmallLoader.tsx", "hash": "257930618007c2c1cfa30aab51cabfcd72049d8b"}, {"file": "apps/assignment/manage-assignment/config/store.ts", "hash": "74f7c911e00817085cee798ee138b19e26c55803"}, {"file": "apps/assignment/manage-assignment/config/utils.ts", "hash": "09ee75195c4f203b88ff96b8a0835785baaa398e"}, {"file": "apps/assignment/manage-assignment/constant/api.ts", "hash": "2259970688de17da8ad2448b1800fb9bf523dadc"}, {"file": "apps/assignment/manage-assignment/hook/common/use-outside-click.ts", "hash": "0c660d2ab34b8be598adac493b943cefa58420ba"}, {"file": "apps/assignment/manage-assignment/hook/common/useWindowSize.ts", "hash": "7c3e1b7eef956c6f9e0ade07c3f49ade0fef8461"}, {"file": "apps/assignment/manage-assignment/hook/deleteData.ts", "hash": "88cf85a404ae00b5895169db2dcfed31856d0d69"}, {"file": "apps/assignment/manage-assignment/hook/fetchData.tsx", "hash": "85c533c5ebd84e7f3def60b69890bc0b46c40fd4", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-assignment/hook/postData.ts", "hash": "2fc1a2e0a7cabc42ff5dabe15c060c8d87f6d104", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-assignment/hook/putData.tsx", "hash": "099d3b329378375e7b5179698ccd420f911fe831"}, {"file": "apps/assignment/manage-assignment/hook/validateUrl.ts", "hash": "c2891576e7fcc592b320fc08a42df3bbed3d0dee"}, {"file": "apps/assignment/manage-assignment/images/addQuestionTestCase.svg", "hash": "049fbd5e0d415f34154c532984cd21bfc69fe25b"}, {"file": "apps/assignment/manage-assignment/images/avatar.png", "hash": "7939878d1a2033e40a4a5b18d3764d2a43df922d"}, {"file": "apps/assignment/manage-assignment/images/avatar.svg", "hash": "a6dfdace3013cadec974a9b5e89b193268aa4c0a"}, {"file": "apps/assignment/manage-assignment/images/avatar1.png", "hash": "82c4de138676eaecc616100bc6b2632d6b795051"}, {"file": "apps/assignment/manage-assignment/images/avatar2.png", "hash": "23754d8f5dc0f4b0fb433edc253be14c026061b9"}, {"file": "apps/assignment/manage-assignment/images/crossQuestionWhite.svg", "hash": "17313cb6b377e6cda60d23668ccde03ba4f13517"}, {"file": "apps/assignment/manage-assignment/images/dropDownIconCommon.svg", "hash": "4fdf0f34a073f9ccbc10465f738d09a7c2b03c67"}, {"file": "apps/assignment/manage-assignment/images/dropDownSelected.svg", "hash": "b809459cda9e3712ed42723eef481ecba49dfa31"}, {"file": "apps/assignment/manage-assignment/images/help.svg", "hash": "a8bc020aeedf5c7e71afe4d705fa5a3f268767f5"}, {"file": "apps/assignment/manage-assignment/images/icon/arrow.svg", "hash": "fbeef4dbbe69d50dfe86cd447668c7c919497712"}, {"file": "apps/assignment/manage-assignment/images/icon/cancel.svg", "hash": "01cd5ccb386860167e9a05af94395567c0d3ad5a"}, {"file": "apps/assignment/manage-assignment/images/icon/cross.svg", "hash": "29eb241f176250b11532e4723b8bcd181195a954"}, {"file": "apps/assignment/manage-assignment/images/icon/empty.svg", "hash": "aa2d2cc953da682d3f0c9cbaaa6f0a6811a90b3e"}, {"file": "apps/assignment/manage-assignment/images/icon/plus_ic.svg", "hash": "cd242dafa9819a62487dac97e59122449808d6e0"}, {"file": "apps/assignment/manage-assignment/images/icon/removeValue.svg", "hash": "a928d35cc8fdd642b877c86c6e7aa01fba1ce5ff"}, {"file": "apps/assignment/manage-assignment/images/search.svg", "hash": "a5d046a0d471e5c0f67d334494863fba404093b6"}, {"file": "apps/assignment/manage-assignment/images/success.svg", "hash": "e6ec8dfbaf1db424d621cf7c4d60d8ad9b90a460"}, {"file": "apps/assignment/manage-assignment/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/assignment/manage-assignment/package.json", "hash": "64b1b393964f3459fab803a7ad174073282a5f38", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/assignment/manage-assignment/screen/Home.tsx", "hash": "0e25826cc3e3f68d46f5ad1cf8b31f923f1d68c7", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-assignment/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/assignment/manage-assignment/src/root.component.tsx", "hash": "26b8ffb7c505992e080a424a607d8edafcc891c0", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-assignment/src/urecruits-manage-assignment.tsx", "hash": "d547865af15936e51ef0b87b715a632d0deee3a5", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-assignment/store/assignment/assignment.actions.ts", "hash": "6b959cd955d42e87be0b2ffa35e684c0cc739b41"}, {"file": "apps/assignment/manage-assignment/store/assignment/assignment.reducer.ts", "hash": "a34ed2af127ccef3575f90f2e33de095f958d282"}, {"file": "apps/assignment/manage-assignment/store/assignment/assignment.selectors.ts", "hash": "f5de660c0b778aef50acb899ca7d11f8955d52b5"}, {"file": "apps/assignment/manage-assignment/store/modal/modal.actions.ts", "hash": "e709ec9c960e3976bab5f78eadf608ab36dcc833"}, {"file": "apps/assignment/manage-assignment/store/modal/modal.reducer.ts", "hash": "8f6a96cf0019261c98f30b759c2d4b121841ee77"}, {"file": "apps/assignment/manage-assignment/store/modal/modal.selectors.ts", "hash": "89fe71f7e048470834b6578199d72f2e95722857"}, {"file": "apps/assignment/manage-assignment/store/root-reducer.ts", "hash": "0b98a7f7c0dde9a65bdea930904bbb736bb6ddee"}, {"file": "apps/assignment/manage-assignment/styles/_button-assessments.scss", "hash": "80bc036e8979493aa58b206d72bf7f2955f50eb7"}, {"file": "apps/assignment/manage-assignment/styles/_color-palette.scss", "hash": "e2828064a49a1a1b1bb806117491b0d95da271e2"}, {"file": "apps/assignment/manage-assignment/styles/_config.scss", "hash": "3f60b8a9c2cf3ef2bb1b436ae69a7ea81ddb4dbf"}, {"file": "apps/assignment/manage-assignment/styles/_loader.scss", "hash": "d0a239563c630323df5f128ee3f77ffd283a624c"}, {"file": "apps/assignment/manage-assignment/styles/_mixins.scss", "hash": "4a1157269cd2a75b3ef5e530e70c2dc621e4a75f"}, {"file": "apps/assignment/manage-assignment/styles/_small-loader.scss", "hash": "3345061e3f2da801e9639dc3fb8d3f2ff64ae10f"}, {"file": "apps/assignment/manage-assignment/styles/components/_elements/customSelect.scss", "hash": "97955652582ae7908245a7b2a8d60d36e89dd8a6"}, {"file": "apps/assignment/manage-assignment/styles/components/checkbox.scss", "hash": "00c24c8e30f4035bf08fa082695cb18510dd442e"}, {"file": "apps/assignment/manage-assignment/styles/components/dialog.scss", "hash": "362922fef7de2b0943e98c825e0456dac95323ea"}, {"file": "apps/assignment/manage-assignment/styles/components/modal.scss", "hash": "875e75926169083934506d9d343e603d81cfc9a7"}, {"file": "apps/assignment/manage-assignment/styles/main.scss", "hash": "42ca78acc1f2be405cd55de16c17d1a5350e062f"}, {"file": "apps/assignment/manage-assignment/styles/MultySelectStyle.ts", "hash": "2a4842e9686526ffcc70007a07bbbfe652118ba1"}, {"file": "apps/assignment/manage-assignment/styles/selectCustomStyle.ts", "hash": "342b65b8a9249338c0138b3cb52252998c1391e4"}, {"file": "apps/assignment/manage-assignment/tsconfig.json", "hash": "ebe7434b7a1d017a3916acb4d8978accafd0d397"}, {"file": "apps/assignment/manage-assignment/types/assignment/actions.types.ts", "hash": "af6b1e44a74da1d0a9b5820efaa6b0ab2beda281"}, {"file": "apps/assignment/manage-assignment/types/assignment/data.types.ts", "hash": "3e6fddbbdddd453d19e08e84887ec6fc49a1bb48"}, {"file": "apps/assignment/manage-assignment/types/assignment/index.ts", "hash": "5015b8ee5ec0464a09f3b3adb5a85afd4fc92d1f"}, {"file": "apps/assignment/manage-assignment/types/index.ts", "hash": "8e1410196578746309180ec7b05829bed2182a49"}, {"file": "apps/assignment/manage-assignment/types/modal/actions.types.ts", "hash": "8b40a44e1dede3ae0d9c477ac0099b30774741dd"}, {"file": "apps/assignment/manage-assignment/types/modal/data.types.ts", "hash": "8ac5ba3b78edf64b6aaa1f1df7bea27d4a4438b0"}, {"file": "apps/assignment/manage-assignment/types/modal/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assignment/manage-assignment/webpack.config.js", "hash": "37e239007331551e3af14f2882e4a0bb493376f8"}]}}, "profile-setting": {"name": "profile-setting", "type": "lib", "data": {"root": "apps/recruitment/profile-setting", "sourceRoot": "apps/recruitment/profile-setting", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/recruitment/profile-setting/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/recruitment/profile-setting/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/recruitment/profile-setting/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/recruitment/profile-setting/component/candidate/awards/AwardsDetailsTab.tsx", "hash": "997edfc44731a63e25cace9722413f653354fadd", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/awards/AwardsDetailsTabItem.tsx", "hash": "a4a83437b308e530f06bee22f674f7ce8280bb05", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/CareerPreferencesTab.tsx", "hash": "14d1636215eb739405805fe1550b3e9cb7e41643", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/certificates/CertificatesDetailsTab.tsx", "hash": "3fe037e07896a43e927e5e8cdd0b45b700c67460", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/certificates/CertificatesDetailsTabItem.tsx", "hash": "da6585fcfee37a792d9f9600b3a372c94cdeb71e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/education/EducationsDetailsTab.tsx", "hash": "e125a765203e993a018dfaff9f3efdc399192da8", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/education/EducationsDetailsTabItem.tsx", "hash": "220f467cd4bba8c34aa28eed799c16b90ea2cf4a", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/candidate/experience/ExperienceDetailsTab.tsx", "hash": "ef1b24beef1ff1c78c9b80e22372c00de367aafb", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/experience/ExperienceDetailsTabItem.tsx", "hash": "636c5e9f080e208682133cfe9d96e70ae4f40e18", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/ManagePasswordTab.tsx", "hash": "359c8ff3580c321ebefb47a81d98f9af56b1de5d", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/candidate/PersonalDetailsTabAndCv.tsx", "hash": "8f409726221f841f962b430abb43100c7a33f9e5", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/PopupPictureUpload.tsx", "hash": "9dfc190f66a5685034139fa10f924fbcedd0ea2e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/projects/ProjectsDetailsTab.tsx", "hash": "43314e09d3e738efc557dd0febc13d7bcf088ee6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/projects/ProjectsDetailsTabItem.tsx", "hash": "eb59fb19df8a0c3d7dbbdd4cdf293720068d0268", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/candidate/QuickViewTab.tsx", "hash": "c521dc678306389fbfe2e14fd3aecd6dc69b176d", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/candidate/skills/SkillsDetailsTab.tsx", "hash": "71de7c554b7ec7f5ed92e94350291d1f703362f8", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/candidate/skills/SkillsDetailsTabItem.tsx", "hash": "8a9761d716b441385c8693331a615d873faeeae2", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/candidate/VisaInformationTab.tsx", "hash": "0d51f6ee0942d867dd65f98e82e7a5de53dc0752", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/access/AccessManagementHead.tsx", "hash": "9ead4e1a40cc1f0a0b472a904d91a39e4f6d3fb5", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/access/AccessManagementTab.tsx", "hash": "8a2e5c92c06314602e2c9526e2abab873e0e6953", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/access/ChangeRolePopup.tsx", "hash": "14a5df10cc8916324e3f9a124ce939fab3e8aa55", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/access/DeleteUserPopup.tsx", "hash": "0addf4f2c035303be1d54601d500ca5e26d4533a", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/access/MembersTable/EmptySearch.tsx", "hash": "dffa99d10ed150e01420a5b0fd2b728a8731583f"}, {"file": "apps/recruitment/profile-setting/component/company/access/MembersTable/MembersBottom.tsx", "hash": "8e2eebb1bf64d447957417c8d76e571e96fea013"}, {"file": "apps/recruitment/profile-setting/component/company/access/MembersTable/MembersTable.tsx", "hash": "7f3cbe6210d5884f5212446a43bdd57bf9787351", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/access/MembersTable/MembersTableBody.tsx", "hash": "d7bdfdda7415e81349f2e9c6cb2d9a323c86efba"}, {"file": "apps/recruitment/profile-setting/component/company/access/MembersTable/MembersTableHead.tsx", "hash": "57d1e585ca6b0905e203d4263f6a77b8b936d7ab"}, {"file": "apps/recruitment/profile-setting/component/company/access/MembersTable/MembersTableRow.tsx", "hash": "34cce217d517ef8dc51fef33ff3c315a0727eaf9", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/access/MembersTable/MembersTop.tsx", "hash": "a35acbe7ccb15562896a2c0311345253dba11233"}, {"file": "apps/recruitment/profile-setting/component/company/access/PerimissionsTable/PermissionsTable.tsx", "hash": "12721d797b976a82bee784b1c092f0246ffce62e", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/access/PerimissionsTable/PermissionsTableItem.tsx", "hash": "a1bcb0a310b6c15b17559fdbbacbbb338ba1ea27", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/access/PerimissionsTable/PermissionSwitcher.tsx", "hash": "5852b6a42486cefc15944bd73e30a06382f316a2"}, {"file": "apps/recruitment/profile-setting/component/company/access/PerimissionsTable/usePermissionsTableItem.ts", "hash": "4ad1a72238749ce2451279f45ca73960636e9e79", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/access/RolesTable/RolesTable.tsx", "hash": "d1e08fde827ba24e7803485a72557a9a0d29b493"}, {"file": "apps/recruitment/profile-setting/component/company/access/RolesTable/RolesTableHead.tsx", "hash": "e6bf29356b97640fcc1278c830c85e58c89db154"}, {"file": "apps/recruitment/profile-setting/component/company/access/RolesTable/RolesTableRow.tsx", "hash": "dd1da50c48068cdf0ca58052786fb318a58e38f4", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/DeletePicturePopup.tsx", "hash": "f1d329de40be05ef5c0afd0f52bf36ea4e430d8c", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/detail/CompanyAddressItem.tsx", "hash": "5faaaffdce7fc5bdff502a666e432e2c32733bc9", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/detail/CompanyDetail.tsx", "hash": "4788c2b7cd792ece922f148d3281cac067bf4546", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/detail/Gallery.tsx", "hash": "68da0bc43d3a3864fc722022c458afca0d40aeca"}, {"file": "apps/recruitment/profile-setting/component/company/integrations/HellosignCard.tsx", "hash": "82df300474a8c17763f8ebe69a685b7964cc33f7", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/integrations/IntegrationCard.tsx", "hash": "85572823a20710a7737532453c9f380b5f27e4d9", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/integrations/IntegrationItem.tsx", "hash": "0c2ac47a7c1b52b6dd54f8465e53dd314f6e7a47", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/integrations/Integrations.tsx", "hash": "f9fff9648c0cfff728cd92bae9de4c12ee3eecbf", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/integrations/JobTargetCard.tsx", "hash": "0ceee13bf987561f8b52cc63c31fa3a4d484d6b3", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/integrations/UniversalCard.tsx", "hash": "0bec63ac1322d89d4d6304c7564e4b1ee764f6a6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/ManagePasswordTab.tsx", "hash": "35f6eb8f6a7da1715a40f97db3ff80afeb9a1f7d", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/ModuleAccessPopup.tsx", "hash": "176b76db3bd64dc6acdfad0b68dce2e2ebcedc0a", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/payment/PaymentCardItem.tsx", "hash": "6999e2dee883a3fb1be41e615e14c009011a48f2"}, {"file": "apps/recruitment/profile-setting/component/company/payment/PaymentChangeForm.tsx", "hash": "cbf065330e11fe1255e45d52f2d447d2641da4be", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/payment/PaymentCreateForm.tsx", "hash": "3d2a1d3558b8899e1f08e84de9b3752ac4b83d20", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/payment/PaymentMethodsTab.tsx", "hash": "011dad1121f3c41c5b0380eaf1fe1db4a0fec2ab", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/PopupGalleryItem.tsx", "hash": "535f06a6a9dc4c3c237fabe4b29ecd388eff6957"}, {"file": "apps/recruitment/profile-setting/component/company/PopupGalleryUpload.tsx", "hash": "913fb71489e13ac6143f593a2cc3ad0329c442f6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/PopupPictureUpload.tsx", "hash": "ad10ea8bfc4c8d21f9ff4b1054598e262ddef4e1", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/CancelSubscriptionPopup.tsx", "hash": "52094092b5e778eaa26569dff12c1366455e49c7", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/InvoiceHistoryComponent.tsx", "hash": "88962f3d95f1ae87ee5d6295495e75be185dbb50", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/InvoicesCard.tsx", "hash": "f7f95663e6b6d9a8204f58c95418d23a53c04c62", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/PaymentSystemPopup.tsx", "hash": "00df45cf6eb7e588ec95ac4bf987c8794a9bc4b5", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/SubscriptionCard.tsx", "hash": "eafb1c78569fe037bb531de4c71973cff96a869c", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/SubscriptionTab.tsx", "hash": "4e43c3fbeaa700adeca4669ddd1c441a4fa731a2", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/UpcomingInvoiceComponent.tsx", "hash": "05f9506f78e1f09a74814748b3ac0b3976384db1", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/company/subscription/UsagePopup.tsx", "hash": "dc1426f73bc0f666dd05fafbbc19230af7b5e808"}, {"file": "apps/recruitment/profile-setting/component/DeletePopup.tsx", "hash": "be81909178998d96f46a1a6abfdf50bda5fecaea", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/Loader.tsx", "hash": "952ac9312bd15ffc2a816aa80b692561c60ff580"}, {"file": "apps/recruitment/profile-setting/component/ProfileNotification.tsx", "hash": "b178653e6cf873a9b960837b13d9d6120ecd84b2"}, {"file": "apps/recruitment/profile-setting/component/recruiter/educations/EducationsDetailsTab.tsx", "hash": "0bfdd592025d5b020237c6aad52d9be0e89520ed", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/educations/EducationsDetailsTabItem.tsx", "hash": "9ea5c771e77a0d937d90c629c990f97b2dc5af33", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/experience/ExperienceDetailsTab.tsx", "hash": "7383664898f358531a23b0dd4da6508938924673", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/experience/ExperienceDetailsTabItem.tsx", "hash": "cfa8734d1245a73a9e50f64985f6480121bf0461", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/ManagePasswordTab.tsx", "hash": "666e86b447f4d745dcfda0aa74c43f19d9e7bf06", "deps": ["api"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/PersonalDetailsTab.tsx", "hash": "1be1e47d7a265590e87efcae66845a2d9bcc5439", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/PopupPictureUpload.tsx", "hash": "00847feba8bf0f672e20fba40888e38ea5eebe5c", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/QuickViewTab.tsx", "hash": "bd5138b4aca310c350d77b0f637e7e6f56b8abb6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/recruiter/VisaInformationTab.tsx", "hash": "7e8741bb1cc86bb295b7649762cf0839a73eb66f", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/component/RichTextEditor.tsx", "hash": "928ffdb42ed7f08292db83995b4f86be261adbe0"}, {"file": "apps/recruitment/profile-setting/component/SelectRoles/SelectRoles.tsx", "hash": "f8855eebae4c6b98bd62cb1d863b5cf2159f32be"}, {"file": "apps/recruitment/profile-setting/component/SelectRoles/SelectRolesItem.tsx", "hash": "0cca88e7a45e000898296281d7663988fff4dd94", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/profile-setting/component/SmallLoader.tsx", "hash": "f439077c82e7770d2d860e818461267a06250c44"}, {"file": "apps/recruitment/profile-setting/constant/currency.ts", "hash": "427d3540094fc7bd47f778482c547d91d276cd99"}, {"file": "apps/recruitment/profile-setting/hook/convertDataToString.ts", "hash": "3ce77b918523d1f7ccfc149ae8adb351827d28a3"}, {"file": "apps/recruitment/profile-setting/hook/deleteData.ts", "hash": "a6a1363cc14c4af6047ed97605fa74c4d6c3657a"}, {"file": "apps/recruitment/profile-setting/hook/fetchData.tsx", "hash": "51bf9fcaca6ef80315afedaf0dad987fa73a5e1b", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/hook/patchData.ts", "hash": "cc03dc95dfe0241227ad83df158872fc0954c96b"}, {"file": "apps/recruitment/profile-setting/hook/postData.ts", "hash": "2fc1a2e0a7cabc42ff5dabe15c060c8d87f6d104", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/hook/putData.tsx", "hash": "a402e76d9bdcb4e92071a4265987ccc8a85693b9", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/hook/sbsPasswordCheckForErrors.ts", "hash": "6214335c73a9db2be6f0f092b6ced26f86eca434"}, {"file": "apps/recruitment/profile-setting/hook/selectSearchFunc.ts", "hash": "eed176a35953dd5599c04117b26c8a2d27a5038f", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/profile-setting/hook/validateEmail.ts", "hash": "b37f54df684a4f6ff4869afd98351fc48487b22b"}, {"file": "apps/recruitment/profile-setting/hook/validatePassword.ts", "hash": "b8d32d92894d9f36746a50b219b4af176fb4d26a"}, {"file": "apps/recruitment/profile-setting/hook/validatePhoneNumber.ts", "hash": "46b3bd6cde924c76817c00ed873842b8134d1d87"}, {"file": "apps/recruitment/profile-setting/hook/validateUrl.ts", "hash": "9da1c69c87458de6de45cd0217e2f352d1a9b6b4"}, {"file": "apps/recruitment/profile-setting/image/icon/aexpress_icon.svg", "hash": "1b40fb9500e7aeeeba72b9408ded476df17a07a4"}, {"file": "apps/recruitment/profile-setting/image/icon/arrow_back_ic.svg", "hash": "68ecbd94243e850bc9fd4498c871140cf14c88af"}, {"file": "apps/recruitment/profile-setting/image/icon/avatar.svg", "hash": "65a2e59f894b3a07e5ccf314a937401487aeb2db"}, {"file": "apps/recruitment/profile-setting/image/icon/calendar_ic.svg", "hash": "05f6771e1475c792445c092090645cc6b0dfc105"}, {"file": "apps/recruitment/profile-setting/image/icon/camera_ic.svg", "hash": "460259fa3fad5b13b014b0a063da50950e1dad0b"}, {"file": "apps/recruitment/profile-setting/image/icon/chose_ic_white.svg", "hash": "01d3064600b26730ef7ce69bda6154a488b1759c"}, {"file": "apps/recruitment/profile-setting/image/icon/chose_ic.svg", "hash": "6f1b1bbe960a9c4e265732020cb1416ee294fdf9"}, {"file": "apps/recruitment/profile-setting/image/icon/close_ic.svg", "hash": "bd4a227d691c0f0d837118d18b941a2e1426383e"}, {"file": "apps/recruitment/profile-setting/image/icon/copy-link_ic.svg", "hash": "e09ea42289056c85adf5ced60d5891649e11c5d6"}, {"file": "apps/recruitment/profile-setting/image/icon/delete_ic_white.svg", "hash": "390cfbdc1ff1a149474e369a648432023ea4c9c7"}, {"file": "apps/recruitment/profile-setting/image/icon/delete_ic.svg", "hash": "9ca29516d488f6e4ebdb1a717a11938013003421"}, {"file": "apps/recruitment/profile-setting/image/icon/delete_image.svg", "hash": "87b631b5cb988d35da250e250c9f7a00f2d31955"}, {"file": "apps/recruitment/profile-setting/image/icon/diners_ic.svg", "hash": "54f3f5e60cda07e81d184226c2547a81e2cf04e6"}, {"file": "apps/recruitment/profile-setting/image/icon/discover_icon.svg", "hash": "89e82f5fe045e05073ab6c47c95d88cd39cf51fd"}, {"file": "apps/recruitment/profile-setting/image/icon/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/recruitment/profile-setting/image/icon/download_ic.svg", "hash": "9af7231119296b2e183f193514a821a1c206c88e"}, {"file": "apps/recruitment/profile-setting/image/icon/edit_green_ic.svg", "hash": "c48cc19062d2ac41575c5c247835cca8c808706f"}, {"file": "apps/recruitment/profile-setting/image/icon/edit_ic.svg", "hash": "4da2e5d0dfc6642794db97fe3fbba3ea63e30c0a"}, {"file": "apps/recruitment/profile-setting/image/icon/edit_red_ic.svg", "hash": "8e0e3a1cdee2a0cb1d68fda61ac61cc00371f0e0"}, {"file": "apps/recruitment/profile-setting/image/icon/eye_icon.svg", "hash": "13a3f027efff31127ef4f772d7d2b08c2eef23c7"}, {"file": "apps/recruitment/profile-setting/image/icon/facebook_ic.svg", "hash": "dd6aaa11d276571e57abf128d2a962430cb54ea9"}, {"file": "apps/recruitment/profile-setting/image/icon/filter-arrows.svg", "hash": "179f6a9fd6dc2b6221d3b86566e14e576f99b5a7"}, {"file": "apps/recruitment/profile-setting/image/icon/form-arrow_ic.svg", "hash": "7c79ca85c67951bbd9379a2de97ccf3ed6a8c2dc"}, {"file": "apps/recruitment/profile-setting/image/icon/hide_password_ic.svg", "hash": "c2948ea1dc72befc0bc35dd9379fdf14bbd42d6d"}, {"file": "apps/recruitment/profile-setting/image/icon/instagram_ic.svg", "hash": "ec7ea99805cfe319afc38782d5f592187bac516a"}, {"file": "apps/recruitment/profile-setting/image/icon/jcb_ic.svg", "hash": "a6e404c10decdc77ae5026656d5b94bb9b091f39"}, {"file": "apps/recruitment/profile-setting/image/icon/linkedIn_ic.svg", "hash": "6c10f6857f4125a930eb5060d048044dc86fce3f"}, {"file": "apps/recruitment/profile-setting/image/icon/mastercard_ic.svg", "hash": "6455928e9bca8360648c1aba84b822393ab8e944"}, {"file": "apps/recruitment/profile-setting/image/icon/mastercard_small_ic.svg", "hash": "438b41e267cd941942ae6b1f09d9c7e08801ff17"}, {"file": "apps/recruitment/profile-setting/image/icon/open_password_ic.svg", "hash": "047171ce0badf1286459c8a523e518b0871161ed"}, {"file": "apps/recruitment/profile-setting/image/icon/paper_clip_ic.svg", "hash": "c6608cd04f99211f58b1082a4953151bca3fc098"}, {"file": "apps/recruitment/profile-setting/image/icon/plus_ic.svg", "hash": "cd242dafa9819a62487dac97e59122449808d6e0"}, {"file": "apps/recruitment/profile-setting/image/icon/red_warning.svg", "hash": "c0df825c275f1d54faee24c7edea305b22d2573e"}, {"file": "apps/recruitment/profile-setting/image/icon/search_ic.svg", "hash": "885b2d6b457b5b51f2d2823e7da7d47fbd899164"}, {"file": "apps/recruitment/profile-setting/image/icon/small_done_ic.svg", "hash": "8d1a6c4d06667a0bdddb5e33a7ebd211d68ab3b6"}, {"file": "apps/recruitment/profile-setting/image/icon/small-arrow-down_ic.svg", "hash": "cd0c507ded5bbc4bd211c189ecb4e427bf328889"}, {"file": "apps/recruitment/profile-setting/image/icon/success_image.svg", "hash": "524f030321ec0c5779e19acf20e0d13f05263535"}, {"file": "apps/recruitment/profile-setting/image/icon/twitter_ic.svg", "hash": "3587f9727842f0d8ac3bf968420dd8f26bb1ca5b"}, {"file": "apps/recruitment/profile-setting/image/icon/union_icon.svg", "hash": "50118bdac983b07097af8e9220f6adc965fd6aa7"}, {"file": "apps/recruitment/profile-setting/image/icon/upload-cloud_ic.svg", "hash": "6d791d28cffd16a3b64964a42d5dd06fb027de1b"}, {"file": "apps/recruitment/profile-setting/image/icon/visa_black_ic.svg", "hash": "b51c4929c24dab2815df62b312c7a21d53768f52"}, {"file": "apps/recruitment/profile-setting/image/icon/visa_ic.svg", "hash": "a1c804c6a728ea4d4e092e33da6b1050926e3de5"}, {"file": "apps/recruitment/profile-setting/image/icon/visa_small_ic.svg", "hash": "90c619e3b3fdf642f84370e36775ce7b0cefa1e4"}, {"file": "apps/recruitment/profile-setting/image/mockup-images/avatar.png", "hash": "385c63e825c424e60ae6fc6cc96a84c5f4612a77"}, {"file": "apps/recruitment/profile-setting/image/none_subscription.svg", "hash": "a041037c9f08ad11144448c9b395c8c9f6357a3a"}, {"file": "apps/recruitment/profile-setting/image/search-empty.svg", "hash": "bf8889a7b0351f1d499544566113fc0b678bb3f4"}, {"file": "apps/recruitment/profile-setting/image/table-empty.svg", "hash": "8557a7bff9c7b59f7cd697a2188d5c6139ae55dc"}, {"file": "apps/recruitment/profile-setting/image/user.png", "hash": "185071bb5b890a00d4210d03587ff84fab0f19eb"}, {"file": "apps/recruitment/profile-setting/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/recruitment/profile-setting/package.json", "hash": "f2b61aee733667a0df7d401393533b7ac893e435", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/recruitment/profile-setting/screen/CandidateProfileScreen.tsx", "hash": "694474cc721b164c61005cf18c07559b195ddd95", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/screen/CompanyProfileScreen.tsx", "hash": "7202872e8df00d2f25860ea6f24c74245b6eee8e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/screen/RecruiterProfileScreen.tsx", "hash": "21ce93d494572ee84723bbdea103b626cac135d5", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/recruitment/profile-setting/src/root.component.tsx", "hash": "b86be2986cceab93a2c5671828038d019ca34411", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/src/urecruits-profile-setting.tsx", "hash": "5f1368b6adce1b40ed34f6dc2524e9c091dd1c2a", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/profile-setting/staticData/positionData.ts", "hash": "e73dbc8edb7ff7a8e11c133ed02c4939f7b37df8"}, {"file": "apps/recruitment/profile-setting/store/index.ts", "hash": "6487aa1df1feabef5f34fad01bac8722b843b40a"}, {"file": "apps/recruitment/profile-setting/store/rolesReducer.ts", "hash": "ea80a85859b0f831c555f0324120acf4cdcadf03"}, {"file": "apps/recruitment/profile-setting/styles/_access-popup.scss", "hash": "eaad03d3a80e33befd6b49f2da44f2496312bac3"}, {"file": "apps/recruitment/profile-setting/styles/_config.scss", "hash": "ca498c144478ff5719d67cb11811ecbcb3d95102"}, {"file": "apps/recruitment/profile-setting/styles/_custom-phone-input.scss", "hash": "38839746226fa485b7b22e05f58b1585b442d802"}, {"file": "apps/recruitment/profile-setting/styles/_delete-user-popup.scss", "hash": "d876d29d994a9a486ff4d6d6b97a1caa84930003"}, {"file": "apps/recruitment/profile-setting/styles/_empty-search.scss", "hash": "24da488e192d457d94c7ad5ad03852dcfe76ae5a"}, {"file": "apps/recruitment/profile-setting/styles/_gallery.scss", "hash": "75ef2b854562c8cbf0c9a7cbe609b67ea812893e"}, {"file": "apps/recruitment/profile-setting/styles/_integrations.scss", "hash": "36a246199871dbd0fdd983d8ec0b3f1d05d006c4"}, {"file": "apps/recruitment/profile-setting/styles/_loader.scss", "hash": "d0a239563c630323df5f128ee3f77ffd283a624c"}, {"file": "apps/recruitment/profile-setting/styles/_members-table.scss", "hash": "77e7b06c74f87fb4b8ac3bb87806ee07d8d0e1fc"}, {"file": "apps/recruitment/profile-setting/styles/_mixins.scss", "hash": "43cf17e9bd6a67bdbe89a3c803aff2087743b818"}, {"file": "apps/recruitment/profile-setting/styles/_payment-method-tab.scss", "hash": "3d24da4fa61c6fde43775aca5a610596fe0593ba"}, {"file": "apps/recruitment/profile-setting/styles/_permission-table.scss", "hash": "8f75ce2413044fb6f3d562751c44323b79b7485d"}, {"file": "apps/recruitment/profile-setting/styles/_popup-picture-upload.scss", "hash": "4c2c28b0a44c31b71a4a6971fb40e0acf3ca8059"}, {"file": "apps/recruitment/profile-setting/styles/_popups.scss", "hash": "6527a74ffbb9fc535ce4e65f4c64bf12cafec091"}, {"file": "apps/recruitment/profile-setting/styles/_profile-setting.scss", "hash": "a7edb0f738e5a9d8e0c4815af52cdad5f77dc211"}, {"file": "apps/recruitment/profile-setting/styles/_react-quill.scss", "hash": "2af03491174f51752bee8c91005274ac57bd92d5"}, {"file": "apps/recruitment/profile-setting/styles/_roles-table.scss", "hash": "b8f7b595dd1c40ff50bb7a7eb9af9adac6a1825e"}, {"file": "apps/recruitment/profile-setting/styles/_select-roles.scss", "hash": "d040cc5d94aa0b4cc078c3dfaba9eb6a63132e28"}, {"file": "apps/recruitment/profile-setting/styles/_small-loader.scss", "hash": "c9a321e845d13b7c7619e041785c7271aa206386"}, {"file": "apps/recruitment/profile-setting/styles/access-management-tab.scss", "hash": "e5b363d80e9c06427fa3dc3aacd019be8b04ef98"}, {"file": "apps/recruitment/profile-setting/styles/datepicker/datepicker.scss", "hash": "4142654d4891dc36dd6ef1426c27d95f73d74864"}, {"file": "apps/recruitment/profile-setting/styles/datepicker/mixins.scss", "hash": "255978e34dbc387795d3b9cda56a2f629c61390c"}, {"file": "apps/recruitment/profile-setting/styles/datepicker/variables.scss", "hash": "48109e34218223e6e229f60f29339fea9a17b78e"}, {"file": "apps/recruitment/profile-setting/styles/main.scss", "hash": "60098b094c48e6650701671d8366469d68f5023e"}, {"file": "apps/recruitment/profile-setting/styles/selectCustomErrorStyle.ts", "hash": "f013a863158e4a270c7733101787a3690c6224da"}, {"file": "apps/recruitment/profile-setting/styles/selectCustomStyle.ts", "hash": "882d96010a89e7c97ca4604887ab8e5ca8195bf2"}, {"file": "apps/recruitment/profile-setting/styles/selectDisableStyle.ts", "hash": "118b5a21cd0ecc90b4789162d4e857ec01cde76a"}, {"file": "apps/recruitment/profile-setting/styles/selectMobileMenuStyle.ts", "hash": "13ff3678aeb4d0ec44aa3867b15621f89fedeb03"}, {"file": "apps/recruitment/profile-setting/styles/selectSmallStyle.ts", "hash": "147446a4e98f42fe6c7b0b1e786d6dae53fadf01"}, {"file": "apps/recruitment/profile-setting/styles/stripeStyle.ts", "hash": "dec138e99bf7300a37a99ac24876fe84cf55611c"}, {"file": "apps/recruitment/profile-setting/styles/subscription-tab.scss", "hash": "3c77f77d4d90ab1f9b14b9cba6d81b2b63e44b40"}, {"file": "apps/recruitment/profile-setting/tsconfig.json", "hash": "a0cc1a78e087f3f2672e69512108195011caf5dc"}, {"file": "apps/recruitment/profile-setting/types/acess-tab/members-table.ts", "hash": "fa64daec992bcb7f28357ffa7f62164680e3db20"}, {"file": "apps/recruitment/profile-setting/webpack.config.js", "hash": "507e0cde5e2a56d37bdb59a52dd207e9ff9e9411"}]}}, "manage-domain": {"name": "manage-domain", "type": "lib", "data": {"root": "apps/assignment/manage-domain", "sourceRoot": "apps/assignment/manage-domain", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "prepare": {"executor": "@nrwl/workspace:run-script", "options": {"script": "prepare"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/assignment/manage-domain/.eslintrc", "hash": "1bf74d3742057ffc653ed4962f71b389e6155e60"}, {"file": "apps/assignment/manage-domain/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/assignment/manage-domain/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/assignment/manage-domain/api/index.js", "hash": "c044671c11c1bf2d296b9759411dafe7fc28de6b"}, {"file": "apps/assignment/manage-domain/api/optionsSelect.ts", "hash": "279a40e1f5c5249e520da20e718bf9301658ecd5"}, {"file": "apps/assignment/manage-domain/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/assignment/manage-domain/components/_elements/InputRange.tsx", "hash": "df3c1e8ab30b8370b3a77dbd9544236e94125b56"}, {"file": "apps/assignment/manage-domain/components/AddedQuestion.tsx", "hash": "7658ed0887346ad5c17ee457485b712048d608a5"}, {"file": "apps/assignment/manage-domain/components/CheckboxButton.tsx", "hash": "32ea7d64995f25876f735498a86f58a6be0f9e3e"}, {"file": "apps/assignment/manage-domain/components/CodingAssessments.tsx", "hash": "16b3a4e28abad40b2c88b8cbbe3b8a92890b30fa"}, {"file": "apps/assignment/manage-domain/components/Dialog.tsx", "hash": "fb67dfb19da42eabafc9c56742b964800cc74584"}, {"file": "apps/assignment/manage-domain/components/Dropdown.tsx", "hash": "bb8e4fa4af01acb5aa1840033526560bd5aaba74"}, {"file": "apps/assignment/manage-domain/components/ExistingAnswer.tsx", "hash": "d73614997297afb767035cedf7aff620de42d6d9"}, {"file": "apps/assignment/manage-domain/components/ExistingDomain.tsx", "hash": "6d5bfc6e28089733aaeb01ccc2328c7ddb1267c6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/components/Input.tsx", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/assignment/manage-domain/components/Loader.tsx", "hash": "b10b2295eafdf90075e4b905f6bdae9b802a27f2"}, {"file": "apps/assignment/manage-domain/components/Modal.tsx", "hash": "8dd580233d28f24e72a82d11bd1297bb14217fca"}, {"file": "apps/assignment/manage-domain/components/MultySelect.tsx", "hash": "1efef18452b0b2461cddadd4f45c2f51d310988e"}, {"file": "apps/assignment/manage-domain/components/Questions.tsx", "hash": "2334adc72b31e1ab492021f69d95b3a1a4d59325"}, {"file": "apps/assignment/manage-domain/components/SearchInput.tsx", "hash": "c7e0d9533d409c53ad16b718cb838cdb0cdaca3f"}, {"file": "apps/assignment/manage-domain/components/SmallLoader.tsx", "hash": "257930618007c2c1cfa30aab51cabfcd72049d8b"}, {"file": "apps/assignment/manage-domain/components/StepOne.tsx", "hash": "ba6615ab5def6a06a366bd79730ceed1f014a716", "deps": ["api"]}, {"file": "apps/assignment/manage-domain/components/StepThree.tsx", "hash": "13d96e30e319296a2545983231163b9634405175", "deps": ["api"]}, {"file": "apps/assignment/manage-domain/components/StepTwo.tsx", "hash": "3b37cb7287de254aa475d7ae2b1d71ae27d424a3", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/config/store.ts", "hash": "74f7c911e00817085cee798ee138b19e26c55803"}, {"file": "apps/assignment/manage-domain/config/utils.ts", "hash": "09ee75195c4f203b88ff96b8a0835785baaa398e"}, {"file": "apps/assignment/manage-domain/constant/api.ts", "hash": "2259970688de17da8ad2448b1800fb9bf523dadc"}, {"file": "apps/assignment/manage-domain/hook/common/use-outside-click.ts", "hash": "0c660d2ab34b8be598adac493b943cefa58420ba"}, {"file": "apps/assignment/manage-domain/hook/common/useWindowSize.ts", "hash": "7c3e1b7eef956c6f9e0ade07c3f49ade0fef8461"}, {"file": "apps/assignment/manage-domain/hook/deleteData.ts", "hash": "88cf85a404ae00b5895169db2dcfed31856d0d69"}, {"file": "apps/assignment/manage-domain/hook/fetchData.tsx", "hash": "85c533c5ebd84e7f3def60b69890bc0b46c40fd4", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/hook/postData.ts", "hash": "2fc1a2e0a7cabc42ff5dabe15c060c8d87f6d104", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/hook/putData.tsx", "hash": "73f3881f51335116265497210f3ed970e1bf8670", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/hook/validateUrl.ts", "hash": "c2891576e7fcc592b320fc08a42df3bbed3d0dee"}, {"file": "apps/assignment/manage-domain/images/addQuestionTestCase.svg", "hash": "049fbd5e0d415f34154c532984cd21bfc69fe25b"}, {"file": "apps/assignment/manage-domain/images/avatar.png", "hash": "7939878d1a2033e40a4a5b18d3764d2a43df922d"}, {"file": "apps/assignment/manage-domain/images/avatar1.png", "hash": "82c4de138676eaecc616100bc6b2632d6b795051"}, {"file": "apps/assignment/manage-domain/images/avatar2.png", "hash": "23754d8f5dc0f4b0fb433edc253be14c026061b9"}, {"file": "apps/assignment/manage-domain/images/circleEmpty.svg", "hash": "****************************************"}, {"file": "apps/assignment/manage-domain/images/circleFill.svg", "hash": "****************************************"}, {"file": "apps/assignment/manage-domain/images/crossQuestionWhite.svg", "hash": "17313cb6b377e6cda60d23668ccde03ba4f13517"}, {"file": "apps/assignment/manage-domain/images/deleteIcon.svg", "hash": "115a0c05f2f590fb945b16e397e4b3086d2942f7"}, {"file": "apps/assignment/manage-domain/images/dropDownIconCommon.svg", "hash": "4fdf0f34a073f9ccbc10465f738d09a7c2b03c67"}, {"file": "apps/assignment/manage-domain/images/dropDownSelected.svg", "hash": "b809459cda9e3712ed42723eef481ecba49dfa31"}, {"file": "apps/assignment/manage-domain/images/editExistingQuestion.svg", "hash": "9805c2c0b570cb79ea3bef2480a56e516f055789"}, {"file": "apps/assignment/manage-domain/images/emptyLine.svg", "hash": "d36b45cf3a02e6d5194dc7d53bbd1ba82cc0c8ed"}, {"file": "apps/assignment/manage-domain/images/firstLine.svg", "hash": "12ff45f81099d16e7ebc8058e1406f4c82d89eeb"}, {"file": "apps/assignment/manage-domain/images/help.svg", "hash": "a8bc020aeedf5c7e71afe4d705fa5a3f268767f5"}, {"file": "apps/assignment/manage-domain/images/icon/arrow.svg", "hash": "fbeef4dbbe69d50dfe86cd447668c7c919497712"}, {"file": "apps/assignment/manage-domain/images/icon/cancel.svg", "hash": "01cd5ccb386860167e9a05af94395567c0d3ad5a"}, {"file": "apps/assignment/manage-domain/images/icon/cross.svg", "hash": "29eb241f176250b11532e4723b8bcd181195a954"}, {"file": "apps/assignment/manage-domain/images/icon/empty.svg", "hash": "aa2d2cc953da682d3f0c9cbaaa6f0a6811a90b3e"}, {"file": "apps/assignment/manage-domain/images/icon/plus_ic.svg", "hash": "cd242dafa9819a62487dac97e59122449808d6e0"}, {"file": "apps/assignment/manage-domain/images/line.svg", "hash": "44c1edb70c492ecfcb4c571d46c6bb6210150684"}, {"file": "apps/assignment/manage-domain/images/radioButton.svg", "hash": "1244d7c0fbe896664339cfd3290bf3b34acaa617"}, {"file": "apps/assignment/manage-domain/images/search.svg", "hash": "a5d046a0d471e5c0f67d334494863fba404093b6"}, {"file": "apps/assignment/manage-domain/images/secondLine.svg", "hash": "50a493ccc9ce3764f7bcc0248c4753e0c9f9d71d"}, {"file": "apps/assignment/manage-domain/images/step.png", "hash": "e57ad7bc35a4657aebe6f36c0268e59c83de62c0"}, {"file": "apps/assignment/manage-domain/images/stepOne.svg", "hash": "****************************************"}, {"file": "apps/assignment/manage-domain/images/stepTwo.svg", "hash": "b47068542d0638fdf1cae4d415d2cdec05cbd69b"}, {"file": "apps/assignment/manage-domain/images/success.svg", "hash": "e6ec8dfbaf1db424d621cf7c4d60d8ad9b90a460"}, {"file": "apps/assignment/manage-domain/images/successQuestion.svg", "hash": "2e4e9a595706510e4c668fa272f3378b4bd5ca94"}, {"file": "apps/assignment/manage-domain/images/textIcon.svg", "hash": "093cae15c012e88aa3e8ff331c1a5304c40db787"}, {"file": "apps/assignment/manage-domain/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/assignment/manage-domain/package.json", "hash": "6f57e8e97916aeb8e16e191baa660ce1e96ad159", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/assignment/manage-domain/screen/Home.tsx", "hash": "fad38d8c0d24280b5fdefd9df1edd2590969397c", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/assignment/manage-domain/src/root.component.tsx", "hash": "e8b05c61206c1305baf73a74de1c438e68aa7257", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/src/urecruits-manage-domain.tsx", "hash": "23e936c0a9916f30e17053d6e8b5a5a7f0c1d4f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assignment/manage-domain/store/domain/domain.actions.ts", "hash": "29ed528be5cbddabcd38d2ba2e64b66a2223c842"}, {"file": "apps/assignment/manage-domain/store/domain/domain.reducer.ts", "hash": "9e9ab336d92633263a14e5f942691effd9a7a34c"}, {"file": "apps/assignment/manage-domain/store/domain/domain.selectors.ts", "hash": "17c0db696fc12579d0c12c868095becff533a510"}, {"file": "apps/assignment/manage-domain/store/existingDomain/existingDomain.actions.ts", "hash": "675351ca5ae503af7062e3d855161efdffbe2c28"}, {"file": "apps/assignment/manage-domain/store/existingDomain/existingDomain.reducers.ts", "hash": "f0cd66648df06412dfe95c86e4dc519000322750"}, {"file": "apps/assignment/manage-domain/store/existingDomain/existingDomain.selectors.ts", "hash": "474c5079bae9604005ae471d2b44e5db238d47f9"}, {"file": "apps/assignment/manage-domain/store/modal/modal.actions.ts", "hash": "2c6b5dfb6c15154854f5e50880659e8197e374f3"}, {"file": "apps/assignment/manage-domain/store/modal/modal.reducer.ts", "hash": "d46d0a50db99b6a7932a3107c996c0cb98e29eb1"}, {"file": "apps/assignment/manage-domain/store/modal/modal.selectors.ts", "hash": "89fe71f7e048470834b6578199d72f2e95722857"}, {"file": "apps/assignment/manage-domain/store/root-reducer.ts", "hash": "983739fa905f6b04ccb9426fec8fe06df2eb1320"}, {"file": "apps/assignment/manage-domain/styles/_button-assessments.scss", "hash": "80bc036e8979493aa58b206d72bf7f2955f50eb7"}, {"file": "apps/assignment/manage-domain/styles/_color-palette.scss", "hash": "e2828064a49a1a1b1bb806117491b0d95da271e2"}, {"file": "apps/assignment/manage-domain/styles/_config.scss", "hash": "3f60b8a9c2cf3ef2bb1b436ae69a7ea81ddb4dbf"}, {"file": "apps/assignment/manage-domain/styles/_loader.scss", "hash": "d0a239563c630323df5f128ee3f77ffd283a624c"}, {"file": "apps/assignment/manage-domain/styles/_mixins.scss", "hash": "4a1157269cd2a75b3ef5e530e70c2dc621e4a75f"}, {"file": "apps/assignment/manage-domain/styles/_popup-assessment.scss", "hash": "39710d1c49d8299f05b1176e16b41983298d4122"}, {"file": "apps/assignment/manage-domain/styles/_small-loader.scss", "hash": "3345061e3f2da801e9639dc3fb8d3f2ff64ae10f"}, {"file": "apps/assignment/manage-domain/styles/components/_elements/customSelect.scss", "hash": "7198a95a873b19ab88d814305633b905dc494105"}, {"file": "apps/assignment/manage-domain/styles/components/_elements/inputRange.scss", "hash": "984e9b34bedddaf967c488bc6454fcfd3df6d16f"}, {"file": "apps/assignment/manage-domain/styles/components/dialog.scss", "hash": "fcc4510881e2867f1a13f0950f7d439f37ce1a16"}, {"file": "apps/assignment/manage-domain/styles/components/existingQuestion.scss", "hash": "652f77b279f0cf915d535a0afb179ef9eee0dc63"}, {"file": "apps/assignment/manage-domain/styles/components/modal.scss", "hash": "1650e3c8cc97a794a6eb127f924451afee5071e8"}, {"file": "apps/assignment/manage-domain/styles/components/question.scss", "hash": "dbc5b36b1b424cb9f06e404e8049506c4aaafeea"}, {"file": "apps/assignment/manage-domain/styles/main.scss", "hash": "3e7d7d3b8e56803db49a69f1ccfbc09a2012a065"}, {"file": "apps/assignment/manage-domain/styles/MultySelectStyle.ts", "hash": "fa1b0520f2c989622ab2d2f5e41e8e42295ddfcc"}, {"file": "apps/assignment/manage-domain/styles/selectCustomStyle.ts", "hash": "b25b9bb7eaf5b4f6202d039452831e319a014bb3"}, {"file": "apps/assignment/manage-domain/tsconfig.json", "hash": "b847c526bd59f04038f938fb69ab93eea176cc54"}, {"file": "apps/assignment/manage-domain/types/domain/actions.types.ts", "hash": "c438bee61a46eaf6d30ca861a56eca9518801e3e"}, {"file": "apps/assignment/manage-domain/types/domain/data.types.ts", "hash": "8d787260083c5e70f1319dddee8319a3b0da91d0"}, {"file": "apps/assignment/manage-domain/types/domain/index.ts", "hash": "5015b8ee5ec0464a09f3b3adb5a85afd4fc92d1f"}, {"file": "apps/assignment/manage-domain/types/existingDomain/actions.types.ts", "hash": "447b6cc3a15b04e0e06fdc9fb0cfd93966401b86"}, {"file": "apps/assignment/manage-domain/types/existingDomain/data.types.ts", "hash": "4238ca3f45a87d16e01f03b6443fde41c6571f2b"}, {"file": "apps/assignment/manage-domain/types/existingDomain/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assignment/manage-domain/types/index.ts", "hash": "bcba187b89772218bb64e6304662e7a1f579cd1d"}, {"file": "apps/assignment/manage-domain/types/modal/actions.types.ts", "hash": "eda3c6676e36ee0e240db0b1be1fcf9447b34bbc"}, {"file": "apps/assignment/manage-domain/types/modal/data.types.ts", "hash": "df082dff1c54b163815af8e8e25ea7433093a8a6"}, {"file": "apps/assignment/manage-domain/types/modal/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assignment/manage-domain/webpack.config.js", "hash": "d46ad6c1098f10bd7ffa58f03f9389b5a0b1eeae"}]}}, "live-coding": {"name": "live-coding", "type": "lib", "data": {"root": "apps/assessment/live-coding", "sourceRoot": "apps/assessment/live-coding", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "prepare": {"executor": "@nrwl/workspace:run-script", "options": {"script": "prepare"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/assessment/live-coding/.eslintrc", "hash": "23249ef0f8894d09b28df1e1257bea06e41fbc4e"}, {"file": "apps/assessment/live-coding/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/assessment/live-coding/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/assessment/live-coding/assets/elements/arrows.tsx", "hash": "dff26cb01e57ac5d9395219dc43deab2f2a9d4f6"}, {"file": "apps/assessment/live-coding/assets/icons/arrow-left.svg", "hash": "d6832760f0679b4ee227cf031dad5612a7eb4045"}, {"file": "apps/assessment/live-coding/assets/icons/arrow-right.svg", "hash": "fb1146ba9e8def03d43aaeec6872d6111c07992c"}, {"file": "apps/assessment/live-coding/assets/icons/attachments.svg", "hash": "47ad0b4010cfe671a8983002017d8b34ff697a09"}, {"file": "apps/assessment/live-coding/assets/icons/calendar.svg", "hash": "7d6d1788fb8a92876b3a8e2ed0f28020e27240e6"}, {"file": "apps/assessment/live-coding/assets/icons/checked.svg", "hash": "dfa16c3df24804736149905662b85257dcf454cf"}, {"file": "apps/assessment/live-coding/assets/icons/clock_ic.svg", "hash": "44160ad0388168ffc40d9fe2c8c456baee20ca74"}, {"file": "apps/assessment/live-coding/assets/icons/congrad.svg", "hash": "c4d6175a9d934af8f9053c42efa178c7951694e2"}, {"file": "apps/assessment/live-coding/assets/icons/consoleArrowBlue.svg", "hash": "cee3815efcb244b3a2db79909a09a66d496241c8"}, {"file": "apps/assessment/live-coding/assets/icons/consoleArrowGray.svg", "hash": "5213bdd397cdf34a65c0b70efea4d3ac2322d945"}, {"file": "apps/assessment/live-coding/assets/icons/consoleArrowRed.svg", "hash": "1f2b6dfc7050064a132a24c2dbdd7653e8511b2e"}, {"file": "apps/assessment/live-coding/assets/icons/consoleArrowYellow.svg", "hash": "b089f866c2e53bb7a2df3a2d589e475a129deeac"}, {"file": "apps/assessment/live-coding/assets/icons/consoleErrorIcon.svg", "hash": "5df0306cae6fb81453eda5e01f04dbeb15df6366"}, {"file": "apps/assessment/live-coding/assets/icons/consoleIcon.svg", "hash": "372baf612057d2b871f1de1651a1109bdd2b0f07"}, {"file": "apps/assessment/live-coding/assets/icons/consoleWarnBlueIcon.svg", "hash": "f145286b5508e4843d65cdc42404077d41130f72"}, {"file": "apps/assessment/live-coding/assets/icons/consoleWarnGrayIcon.svg", "hash": "fe0b5d9496628bc1914429d9fd974f47cfc489a6"}, {"file": "apps/assessment/live-coding/assets/icons/consoleWarnYellowIcon.svg", "hash": "1c1f1a9127a3cb79bebb0818d733c8638358ffe2"}, {"file": "apps/assessment/live-coding/assets/icons/cross.svg", "hash": "29eb241f176250b11532e4723b8bcd181195a954"}, {"file": "apps/assessment/live-coding/assets/icons/dropDownArrow.svg", "hash": "dfd954a615f5c0150fa44422dcea5417334f1164"}, {"file": "apps/assessment/live-coding/assets/icons/dropDownIconCommon.svg", "hash": "f1c0b8007d71712adad30c316794ae1c9390cb84"}, {"file": "apps/assessment/live-coding/assets/icons/dropDownSelected.svg", "hash": "0687baa1819b320f2eca25f18fe60d10a57e277c"}, {"file": "apps/assessment/live-coding/assets/icons/feedback.svg", "hash": "a1108fb4b88f0f826bb08eaae2773e9ab873c111"}, {"file": "apps/assessment/live-coding/assets/icons/getLink.svg", "hash": "97bfebe528f3f76fbda4356b16b556d9d0edd48e"}, {"file": "apps/assessment/live-coding/assets/icons/info.svg", "hash": "9e643ccb595720c3a96a006979526e3c25453f46"}, {"file": "apps/assessment/live-coding/assets/icons/recording.svg", "hash": "03d54be338fcc8230f5b9a3c90f8ee76780e86fd"}, {"file": "apps/assessment/live-coding/assets/icons/reloadIcon.svg", "hash": "d0572d7f1bc151161bd92ca813c087072112ce09"}, {"file": "apps/assessment/live-coding/assets/icons/search.svg", "hash": "5088e0fb9ecff3077af6cec6b3a144304c2bf685"}, {"file": "apps/assessment/live-coding/assets/icons/Send.svg", "hash": "2fa4d483f396714b0cddabc4376692570f75e696"}, {"file": "apps/assessment/live-coding/assets/icons/settingsIcon.svg", "hash": "6de73958540a969d9ca66a90c89042c23a0491cd"}, {"file": "apps/assessment/live-coding/assets/icons/slideArrowL.svg", "hash": "dc76157f53de4c59a22c1e5b594fc9f84aee3598"}, {"file": "apps/assessment/live-coding/assets/icons/slideArrowR.svg", "hash": "1b061dba71fdaab9b26722ca74ef53298680550b"}, {"file": "apps/assessment/live-coding/assets/icons/split-icon-dark.svg", "hash": "d14034a4c0dcbaf66f7f11fcc74e76aef48f5883"}, {"file": "apps/assessment/live-coding/assets/icons/split-icon.svg", "hash": "684034910ce7f87477807b95456196687421139a"}, {"file": "apps/assessment/live-coding/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/assessment/live-coding/components/_elements/Checkbox.tsx", "hash": "a9b3f2cea30f7e423c2f9d98d2e67ffa92554851"}, {"file": "apps/assessment/live-coding/components/_elements/CodeEditor.tsx", "hash": "d9479b29c1334b47889b2c459878a1d434105ec2"}, {"file": "apps/assessment/live-coding/components/_elements/CodingSelect.tsx", "hash": "d7437746cfe704643f33b7167256321774d70284"}, {"file": "apps/assessment/live-coding/components/_elements/Modal.tsx", "hash": "fa469990339eb9f25acd6af7fb1d9b3a78b3decd"}, {"file": "apps/assessment/live-coding/components/_elements/Warnings.tsx", "hash": "1f8383b8f3427292c2a924965aa20655507cb987"}, {"file": "apps/assessment/live-coding/components/code-editor/editor-feedback.tsx", "hash": "a57ee8c27d949578267197747354e55d10662c12"}, {"file": "apps/assessment/live-coding/components/code-editor/editor-footer.tsx", "hash": "6d6bc167aa0cf6ebf580362c56b859390b0578c7", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/code-editor/editor-header.tsx", "hash": "1bba8f422831af8f52dedd9403b24d51e4ad4082"}, {"file": "apps/assessment/live-coding/components/code-editor/editor-offline.tsx", "hash": "2f8fd963578affb64c4d6743eede3258f131fba4", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/code-editor/editor-online.tsx", "hash": "15a7ed6f0d43259cd7a9bff00882d5745ddd962e", "deps": ["api", "coding-assessments", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/code-editor/editor-playback.tsx", "hash": "78ff2bd680d9d5940d47de575a44760d0b3853db", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/code-editor/editor-start-offline.tsx", "hash": "d40d5196d8d9f01594d9c6da22589259f0a863d4", "deps": ["api", "coding-assessments"]}, {"file": "apps/assessment/live-coding/components/code-editor/editorTheme.ts", "hash": "493d98d2002f5f3c3d13b59675081be2425cd285"}, {"file": "apps/assessment/live-coding/components/code-editor/fakeData.ts", "hash": "133425dd4ad1deceb9bf49771f6184c7829b3dbb"}, {"file": "apps/assessment/live-coding/components/code-editor/index.tsx", "hash": "a3d5f81a862ff00ffd5449dae696a87c84e1db6b", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/code-editor/Loader.tsx", "hash": "ac7713d895378a2e7b428c27fb5d0a5243b9eccb"}, {"file": "apps/assessment/live-coding/components/code-editor/mockData/actions.ts", "hash": "f492e01e5c2b36edfdcfea404969ecc567bc928b"}, {"file": "apps/assessment/live-coding/components/code-editor/mockData/assesment.ts", "hash": "6fa9202e77d36555d27496ea14b86515dfc6c091"}, {"file": "apps/assessment/live-coding/components/code-editor/mockData/playback.ts", "hash": "a26502ab8996a9be60aa3d33f0d0344c31fc56ce"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/editor-modals-text.json", "hash": "ea4710780db4d2813ad69b40532edf3aa8639d07"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/editor-modals.tsx", "hash": "0577a941ffadd36a7a004507be557636537e1aa5", "deps": ["api"]}, {"file": "apps/assessment/live-coding/components/code-editor/modals/editor-settings-modal.tsx", "hash": "f517156e3a460c21f400b1b77a06668d22fab876"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/keyboardShortcuts/index.tsx", "hash": "671a0942c82fe962b131eb2f89ee9fc1c5415baf"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/display/display.tsx", "hash": "1e002f3dea26c2e1bb229b733664e6e3078ea68d"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/filters/filters.tsx", "hash": "9598e715a7e084b99ab7e28deb703afecd23e64d", "deps": ["api"]}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/index.tsx", "hash": "daffc028e92576db1a860bc844e79f2c894c9362", "deps": ["api"]}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/questionPage/questionPage-content.tsx", "hash": "ef1d0374236239688e584e036dafde783d85f2f8"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/questionPage/questionPage-header.tsx", "hash": "7bff9a5b4367b7d4b2dfa7d62dd3e69a38cee657"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/questionPage/questionPage.tsx", "hash": "5ae41a55e5a1434b5afefd4dfcb7e93ac3c72c7b"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/questionPage/StarterCodePage.tsx", "hash": "cccbe6ffb6714e469bccc9263608223f26df0e25"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/questions/question_item.tsx", "hash": "df6c403e6b3e2eebe07569a0ecd8ff9842c0b2b3"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/questions/questions.tsx", "hash": "f248bc538deb038de6ff808923152c932898c084", "deps": ["api"]}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/test_endpoints/first-endpoint.json", "hash": "f6c48f05a6da3a4fde7a61dac1af90e1e44f0539"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/test_endpoints/second-endpoint.json", "hash": "95175be5a9df33d9b3c7cb7a452fe5a90facb35d"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/test_endpoints/test-questions-endpoint.json", "hash": "2f7bcbdbed681bf3d9d55767a1c04d1822319e62"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/listOfQuestions/welcomePage/welcomePage.tsx", "hash": "4920d11352515a8817188620e56f155f85d989d9"}, {"file": "apps/assessment/live-coding/components/code-editor/modals/QuestionLoader.tsx", "hash": "6b320858f6d059acb6a8ce3c1e05b492317cc848"}, {"file": "apps/assessment/live-coding/components/code-editor/SmallLoader.tsx", "hash": "f439077c82e7770d2d860e818461267a06250c44"}, {"file": "apps/assessment/live-coding/components/header/CountdownTimer.tsx", "hash": "eb6685bf95a35627a91e55d784e38eecd674f55e"}, {"file": "apps/assessment/live-coding/components/header/index.tsx", "hash": "808945ca217e4218e93d785f935175df7aff9955", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/header/invitionModal/calendar/build.tsx", "hash": "44d48623a5071c59dbee7f38a18bfdd000dbbe55"}, {"file": "apps/assessment/live-coding/components/header/invitionModal/calendar/header.jsx", "hash": "b60ea0b93d4366c15d5a216b6622afcccf642aa4"}, {"file": "apps/assessment/live-coding/components/header/invitionModal/calendar/header.tsx", "hash": "421516d6f163e4339f7a89b61eb9895aa827e9ac"}, {"file": "apps/assessment/live-coding/components/header/invitionModal/calendar/index.tsx", "hash": "d17ed03fdbb7f21185cee266602fcad4fb02e4d6"}, {"file": "apps/assessment/live-coding/components/header/invitionModal/calendar/styles.tsx", "hash": "331e5621f13eb0cf651309aaded1fd669126a0ca"}, {"file": "apps/assessment/live-coding/components/header/invitionModal/Content.tsx", "hash": "d642bcba4684c019ee6aee8f3a854f20a9acf517", "deps": ["api"]}, {"file": "apps/assessment/live-coding/components/header/invitionModal/Footer.tsx", "hash": "5bc2f38b7090e07e172c90b1c9ac4929b804e477", "deps": ["api"]}, {"file": "apps/assessment/live-coding/components/header/invitionModal/Header.tsx", "hash": "deff171af4a8478894ea6249abb2335bdb2b85e2"}, {"file": "apps/assessment/live-coding/components/header/invitionModal/index.tsx", "hash": "cc2de353c89c5cc765ba852b601acec51d29fe8c"}, {"file": "apps/assessment/live-coding/components/sidebar/meeting-sidebar.tsx", "hash": "39bdf9257951a8af426ba237bc4d1cf3f497a75d"}, {"file": "apps/assessment/live-coding/components/task-description/index.tsx", "hash": "db8ab4cd9842dc36e0aa1dac1ba1837c2b4ea05e"}, {"file": "apps/assessment/live-coding/components/task-description/Problem.tsx", "hash": "77dd035cc4990c0632728bee14092866b7d32919"}, {"file": "apps/assessment/live-coding/components/task-description/Terminal.tsx", "hash": "a81ac478efd03dd4412ebe3a22f1f8f1777cb4fa", "deps": ["coding-assessments"]}, {"file": "apps/assessment/live-coding/components/task-description/testCases.tsx", "hash": "ec0e8b520acec1d74b524218e6afb6feead7cdfe", "deps": ["api"]}, {"file": "apps/assessment/live-coding/components/video-meeting/chat.tsx", "hash": "00f974d48cb457c0d9d18e4291ab4977693b9832"}, {"file": "apps/assessment/live-coding/components/video-meeting/InterviewJoinRoom.tsx", "hash": "26d92b48af4985173ca7bf0aaa57a6ec33d5ef7b", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/video-meeting/JoinRoom.tsx", "hash": "2536163fc60072543ed94b5cd79f30b3d0301ff3", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/video-meeting/Loader/VideoMeetLoader.tsx", "hash": "320858ef52943bf525b858afef337dc36c42b226"}, {"file": "apps/assessment/live-coding/components/video-meeting/meetingPreview.tsx", "hash": "1459d3369a2ca29c3079ca2bb5ffcddcca171118", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/components/video-meeting/RemoteParticipant/ParticipantsList.tsx", "hash": "a70c9a63a593ee314d3828387b805f74c8a7a62c"}, {"file": "apps/assessment/live-coding/components/video-meeting/RemoteParticipant/RemoteParticipant.tsx", "hash": "98340bc12f46efca65847960522b87ee914f14f4"}, {"file": "apps/assessment/live-coding/config/firebaseFirestore.ts", "hash": "c7f1e9a2e4adf32384b7612fc0c815e80614599a"}, {"file": "apps/assessment/live-coding/config/store.ts", "hash": "534a09136c593fe2111a93f604ffa7f2de2ebda9"}, {"file": "apps/assessment/live-coding/hooks/api/deleteData.ts", "hash": "a4f69d1a223923d2ff17c4d3378ab75ac7181ab1"}, {"file": "apps/assessment/live-coding/hooks/api/fetchData.tsx", "hash": "85c533c5ebd84e7f3def60b69890bc0b46c40fd4", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/hooks/api/postData.ts", "hash": "4ba6e5d1b66507461707acc1bce9ca6ee7710c38", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/hooks/api/putData.tsx", "hash": "ade7e22c2dbc928e6e18027ac40f29d17fc16948"}, {"file": "apps/assessment/live-coding/hooks/api/validateUrl.ts", "hash": "96a7350a94047ed21f67c988984c02187115ba57"}, {"file": "apps/assessment/live-coding/hooks/common/use-outside-click.ts", "hash": "1dbef47ae7cf706369c8b283e7806e3c34af86ee"}, {"file": "apps/assessment/live-coding/hooks/common/use-window-size.ts", "hash": "aa94c7e1f55149a26da90ffff52e2a4c38a00091"}, {"file": "apps/assessment/live-coding/hooks/editor/code_executor.ts", "hash": "f156a72b4848418eb60f3aeb79769e5d626527c8", "deps": ["api"]}, {"file": "apps/assessment/live-coding/hooks/editor/shared/get-languages.ts", "hash": "8ad19959928e6b9f2d2004f532c713180e83f199"}, {"file": "apps/assessment/live-coding/hooks/editor/use-connect-paticipant.ts", "hash": "37013ef84ed694fbd3dd230c5233119cd62cdbc6"}, {"file": "apps/assessment/live-coding/hooks/editor/use-cursor.ts", "hash": "0422f05ae16e9daf0eab9e32ca9c89f2be927005"}, {"file": "apps/assessment/live-coding/hooks/editor/use-init-editor.ts", "hash": "1309f9bd46ce10692e20dca36bf3fffb897d1637"}, {"file": "apps/assessment/live-coding/hooks/editor/use-playback-actions.ts", "hash": "55421430376a328ba8d47f5f3d8a0a27071cafac"}, {"file": "apps/assessment/live-coding/hooks/editor/use-run-testcases.ts", "hash": "f37f6a46e278f8f0db26d236abfe90bc72ae88c5", "deps": ["coding-assessments", "api"]}, {"file": "apps/assessment/live-coding/hooks/editor/use-selection.ts", "hash": "c45bad43cce51e62d895144c3c6dcf2eba59c7e7"}, {"file": "apps/assessment/live-coding/hooks/editor/use-set-actions.ts", "hash": "6f2960dc9611085cf8c763fb9d0e9196ca5eeeeb"}, {"file": "apps/assessment/live-coding/hooks/editor/use-settings.ts", "hash": "9d88f2310a76b30bd430fdfc4c51c95c6498b8e0"}, {"file": "apps/assessment/live-coding/hooks/editor/use-shared-data.ts", "hash": "9b63b08c792f4cba7ed5820819bed00f515ade64"}, {"file": "apps/assessment/live-coding/hooks/modal/use-modal.ts", "hash": "97377f96c01b1e46f4934513e7dc631a47703b20"}, {"file": "apps/assessment/live-coding/images/icon/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/assessment/live-coding/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/assessment/live-coding/package.json", "hash": "3f9dfdd3e9859f3c68125296a4b9e40264dace79", "deps": ["@ucrecruits/globalstyle", "api", "npm:typescript"]}, {"file": "apps/assessment/live-coding/pages/live-coding.tsx", "hash": "d0dfd6c1e06740688ce6b4c46b632ce4060b4d70", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/pages/offline-coding.tsx", "hash": "2fd1b3e78233ac52c338219237b0bcefc95fd1d4", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/pages/playback.tsx", "hash": "042a993cd0c9f7c81a2476082aaf9c8d5c18e3cd", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/pages/sand-box.tsx", "hash": "90ff45469d683b2c7d4da31695c14a9c7ca35f65", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/pages/video-interview.tsx", "hash": "ed02a35f7148bfb085612bf7bfaab80de1c0f820", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/assessment/live-coding/src/root.component.tsx", "hash": "373c404e73a5c30d1292c6f161d1d566c63a5c9d", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/src/urecruits-live-coding.tsx", "hash": "23e936c0a9916f30e17053d6e8b5a5a7f0c1d4f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/assessment/live-coding/store/assessment/assessment.actions.ts", "hash": "f0d25e364285b80d4e56a8cc136b4a0e714d25fb"}, {"file": "apps/assessment/live-coding/store/assessment/assessment.reducer.ts", "hash": "53e79ba9ab7b1180c30e443d7c76aa07e82a828d"}, {"file": "apps/assessment/live-coding/store/assessment/assessment.selectors.ts", "hash": "02a340eb726ac6a51f7ac8fc52bec4afd87bbf25"}, {"file": "apps/assessment/live-coding/store/conversation/conversation.actions.ts", "hash": "1368865b763f2cffef56c7b1d835fff72b1e7d3f"}, {"file": "apps/assessment/live-coding/store/conversation/conversation.reducer.ts", "hash": "351d2e91665d02267576bb11f63f021067cd0cb6"}, {"file": "apps/assessment/live-coding/store/conversation/conversation.selectors.ts", "hash": "732705e6b3b22c131c35323810150c38eec3a8ee"}, {"file": "apps/assessment/live-coding/store/editor/editor.actions.ts", "hash": "e973ed6cbf1288c7ba416cda583b756cc0714200"}, {"file": "apps/assessment/live-coding/store/editor/editor.reducer.ts", "hash": "9df7930f37a73163379790a1fc31e5500a3b5059"}, {"file": "apps/assessment/live-coding/store/editor/editor.selectors.ts", "hash": "661bac082069d4199d2077fc483705a9f757c675"}, {"file": "apps/assessment/live-coding/store/footer/footer.actions.ts", "hash": "5b1e5f4d47a09fad4a3a306aeb70df98b059147e"}, {"file": "apps/assessment/live-coding/store/footer/footer.reducer.ts", "hash": "9a6c8e3a771c41bd192837721504aebbbe2a66a4"}, {"file": "apps/assessment/live-coding/store/footer/footer.selectors.ts", "hash": "2a0adf1647bc9879235e5594b86baff5e14b77fe"}, {"file": "apps/assessment/live-coding/store/root-reducer.ts", "hash": "4838a424e72f1cf7353443bbcb73ce4f62e34c81"}, {"file": "apps/assessment/live-coding/store/video-meeting/assessment.actions.ts", "hash": "cbff9a0a488bc6e4b447058cd4dab2ccd9850129"}, {"file": "apps/assessment/live-coding/store/video-meeting/assessment.reducer.ts", "hash": "21757f8d98bf4a1293b9189f9984b5e98a304804"}, {"file": "apps/assessment/live-coding/store/video-meeting/assessment.selectors.ts", "hash": "270257eba28c2935257abb3d26c9668c13ebbe7a"}, {"file": "apps/assessment/live-coding/styles/_color-palette.scss", "hash": "26f1e1f3f7852eb703414c2885ce171215a40f4f"}, {"file": "apps/assessment/live-coding/styles/_config.scss", "hash": "7566e008e1e149f957b794d19a1565f2f880e3fd"}, {"file": "apps/assessment/live-coding/styles/_loader.scss", "hash": "839d8c05322e3d6cafe699e1e0bf600504828974"}, {"file": "apps/assessment/live-coding/styles/_mixins.scss", "hash": "f0d7e38cd179ec3d9425e31efec98d7c500fcdf6"}, {"file": "apps/assessment/live-coding/styles/_small-loader.scss", "hash": "3345061e3f2da801e9639dc3fb8d3f2ff64ae10f"}, {"file": "apps/assessment/live-coding/styles/components/_elements/checkbox.scss", "hash": "2ea0c219a6b9edfd4a09cfbf8fe70c0210fac095"}, {"file": "apps/assessment/live-coding/styles/components/_elements/customField.scss", "hash": "f0060b54f3d717b9ccb684432990e26d1aa1f349"}, {"file": "apps/assessment/live-coding/styles/components/_elements/customFieldWithIcon.scss", "hash": "51949950401772f9e1d9cbfe16f56f90d9bfd418"}, {"file": "apps/assessment/live-coding/styles/components/_elements/customSelect.scss", "hash": "a32c16f0533cd41dda0ad4452a3cd17e1ec12d6c"}, {"file": "apps/assessment/live-coding/styles/components/_elements/modal.scss", "hash": "0de0ecb7c814830e15172ca1f585a6819a6f83c1"}, {"file": "apps/assessment/live-coding/styles/components/_elements/warnings.scss", "hash": "6fbb3e700333e8911aa8a842577ebd02bd6c2d78"}, {"file": "apps/assessment/live-coding/styles/components/_interview-room-modal.scss", "hash": "05bbc3acff1ad59203c1b41b4607d08eb6f505e1"}, {"file": "apps/assessment/live-coding/styles/components/_interview.scss", "hash": "237e1eba4a8de26dba6bb9a9727000b9d98e8ab7"}, {"file": "apps/assessment/live-coding/styles/components/_joining-room-modal.scss", "hash": "5d1944657f7db3caee9b0e17a03aba50914b94c2"}, {"file": "apps/assessment/live-coding/styles/components/_meeting-preview.scss", "hash": "04206f96a0e1845d3a27699e44cf615f51138f0e"}, {"file": "apps/assessment/live-coding/styles/components/_participantList.scss", "hash": "15ca491b20a1165608747a8ddba329b8a70600a8"}, {"file": "apps/assessment/live-coding/styles/components/_questionLoader.scss", "hash": "3bfda08a7be5b3a4b77238663222e118ae77f972"}, {"file": "apps/assessment/live-coding/styles/components/_videoMeetLoader.scss", "hash": "a1bf4266b1de55cdd1753ccccf9c88d5b260e733"}, {"file": "apps/assessment/live-coding/styles/components/calendar.scss", "hash": "144c2537d77d3ee3e4fa7ab21ee6dd560e239280"}, {"file": "apps/assessment/live-coding/styles/components/editor/editor-feedback.scss", "hash": "10fe535027b03d2eeda3ee4f96f152f458f7d301"}, {"file": "apps/assessment/live-coding/styles/components/editor/editor-footer.scss", "hash": "0ec2dc4b7f8937f36db28b653d23a5740c305a97"}, {"file": "apps/assessment/live-coding/styles/components/editor/editor-header.scss", "hash": "f0330200444facc7a30cee631366b115ceb483ab"}, {"file": "apps/assessment/live-coding/styles/components/editor/editor-modals.scss", "hash": "c5dbc7208cd1775dacb38e6d597cb1d073fd9ef7"}, {"file": "apps/assessment/live-coding/styles/components/editor/editor-settings-modal.scss", "hash": "5318c82e3a8939cf91f10f646533f76276587ffa"}, {"file": "apps/assessment/live-coding/styles/components/editor/editor-start-offline.scss", "hash": "a52c3e16107e70de47c20ae647b816faa9f03401"}, {"file": "apps/assessment/live-coding/styles/components/editor/editor.scss", "hash": "743f3e2ac8e4b3b5bcd53a196c9883f51f12d1bb"}, {"file": "apps/assessment/live-coding/styles/components/header.scss", "hash": "418ce654d0dfac5b81eb4b630b240705a84464b7"}, {"file": "apps/assessment/live-coding/styles/components/inviteCandidate.scss", "hash": "ed826d0034a00dfde4467266405abbaebc34e131"}, {"file": "apps/assessment/live-coding/styles/components/keyboardShortcuts.scss", "hash": "c4dfaf3b62b0ecd0338e3c451ca79fe8b9656c91"}, {"file": "apps/assessment/live-coding/styles/components/listOfQuestions.scss", "hash": "f021f0685059c5c8f15115be536d9f6ef9ee2324"}, {"file": "apps/assessment/live-coding/styles/components/message-drawer.scss", "hash": "fd35275cb480a0571a4ae10d00c1f6142012fd2d"}, {"file": "apps/assessment/live-coding/styles/components/tabs.scss", "hash": "54b8ed05fd486f6c9e51d1bb4f2316933ea93c81"}, {"file": "apps/assessment/live-coding/styles/components/task-description.scss", "hash": "3002445118a6079d558dbf8e0a17143213b77895"}, {"file": "apps/assessment/live-coding/styles/components/test-cases.scss", "hash": "497418ab31630a604a0689da8f4a75344ca7cda3"}, {"file": "apps/assessment/live-coding/styles/main.scss", "hash": "4e1cde8d64759998f7b8cf7af3cc70c3e606c55d"}, {"file": "apps/assessment/live-coding/styles/main/_button.scss", "hash": "3920748304a5304badc711bc82a8d97d4ef365df"}, {"file": "apps/assessment/live-coding/styles/root.scss", "hash": "636c990c7b213996fcee04dadc0f6e1630ebb79d"}, {"file": "apps/assessment/live-coding/styles/selectCustomStyle.ts", "hash": "631040ffab2e6de8a025b61521ad8083f01e9872"}, {"file": "apps/assessment/live-coding/tsconfig.json", "hash": "106f4bcb9d43701a59d7ab9713a0df9a32341300"}, {"file": "apps/assessment/live-coding/types/assessment/actions.types.ts", "hash": "8b00c30e82a5c8b38fd8b3e3d0a72f60a685baf5"}, {"file": "apps/assessment/live-coding/types/assessment/data.types.ts", "hash": "25afc429a80052d92a338597bb385a74207be6e2"}, {"file": "apps/assessment/live-coding/types/assessment/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assessment/live-coding/types/conversation/actions.types.ts", "hash": "15ae15d7f8bd3fa8f1e7f810838b83c5930c8992"}, {"file": "apps/assessment/live-coding/types/conversation/data.types.ts", "hash": "c8b64a6ddd6a0d9b08cb4285a41ad8c913f3aad9"}, {"file": "apps/assessment/live-coding/types/conversation/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assessment/live-coding/types/editor/actions.types.ts", "hash": "2d98a33b64b4fbe8e2e85052957fb50e94891f71"}, {"file": "apps/assessment/live-coding/types/editor/data.types.ts", "hash": "f717c256d16703490647e70710cdf49e7f3d9cac"}, {"file": "apps/assessment/live-coding/types/editor/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assessment/live-coding/types/footer/actions.types.ts", "hash": "24688f6b2ad7e1bc3961db8713bdc17eea389cc5"}, {"file": "apps/assessment/live-coding/types/footer/data.types.ts", "hash": "04dd058ef854904bbf5c72426e4a1d72d99dde3a"}, {"file": "apps/assessment/live-coding/types/footer/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assessment/live-coding/types/index.ts", "hash": "96b8317edd9ff77721e723183d39b29d0ae9843b"}, {"file": "apps/assessment/live-coding/types/video-meeting/actions.types.ts", "hash": "c3f3c99f08cbd3dde2855b421ca74d96ae3367a4"}, {"file": "apps/assessment/live-coding/types/video-meeting/data.types.ts", "hash": "66e5f415cd19074ca93f08d3e8fb9e1907bd9def"}, {"file": "apps/assessment/live-coding/types/video-meeting/index.ts", "hash": "b91b5f99e1bc9694748364c6ff9ca96152624637"}, {"file": "apps/assessment/live-coding/utils/helpers/checkResize.ts", "hash": "9d78e9006e935a697dd27c54360e9823f6b4c25c"}, {"file": "apps/assessment/live-coding/utils/helpers/getReplacedCodeList.ts", "hash": "994eccb030f236a1812f4e16556eaf31bdb567ca", "deps": ["coding-assessments"]}, {"file": "apps/assessment/live-coding/utils/helpers/handleRunTestCases.ts", "hash": "a0db3154415cb9c2b474c2a05ca6fec7a333d28d", "deps": ["coding-assessments"]}, {"file": "apps/assessment/live-coding/webpack.config.js", "hash": "26439ddd48d77dd3fe6b5f8062dc15e82366e8d2"}]}}, "create-job": {"name": "create-job", "type": "lib", "data": {"root": "apps/recruitment/create-job", "sourceRoot": "apps/recruitment/create-job", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/recruitment/create-job/.eslintrc", "hash": "be8b560b3cfb73c336416f85a7198885a2d5e697"}, {"file": "apps/recruitment/create-job/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/recruitment/create-job/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/recruitment/create-job/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/recruitment/create-job/components/ApplyJob/ApplicationInner.tsx", "hash": "d2e8d06da6236953ad26c3530fa2af4dd72adfc1", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/ApplyJob/ApplyInner.tsx", "hash": "1229c9d8e2607961401f6ea7beb3447e87245ba4", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/ApplyJob/ApplyQuestion.tsx", "hash": "ed1c9d9accf54c12fa914375d0d87726076ba159"}, {"file": "apps/recruitment/create-job/components/ApplyJob/Header.tsx", "hash": "ea6bbf0d0d5b79e1700e83da7ecbc71bd762e01e"}, {"file": "apps/recruitment/create-job/components/ApplyJob/Popups/ReturnToJobPopup.tsx", "hash": "2f9602ac1c8321548aff0d2f7c7e9950f142c035"}, {"file": "apps/recruitment/create-job/components/ApplyJob/Popups/SuccessfullyPopup.tsx", "hash": "f1a4a223725dd0d6f544a4210264d15a781bf70d", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/ApplyJob/Summary.tsx", "hash": "c4cccbc2e7bb8146d87863a0563ba46384012c87", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/ApplicationFormComponents/Answer.tsx", "hash": "4912b44f3dc8c949f5343c4d39d00a713e72050f"}, {"file": "apps/recruitment/create-job/components/CreateJob/ApplicationFormComponents/ApplicationItem.tsx", "hash": "bb46b9ae3099fe18d5f452cca90f35d4304ece3e"}, {"file": "apps/recruitment/create-job/components/CreateJob/ApplicationFormComponents/Question.tsx", "hash": "86e28ddc9510f5ca44a3a821a066b7e5e36467c4"}, {"file": "apps/recruitment/create-job/components/CreateJob/ApplicationFormComponents/QuestionWrap.tsx", "hash": "dd44b331de78b19ce7763cc002d62a0c075c12d2"}, {"file": "apps/recruitment/create-job/components/CreateJob/BoardItem.tsx", "hash": "41dd5467e2ca0d3f699a928049021961ab28daa9", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/create-job/components/CreateJob/CustomRange.tsx", "hash": "86d6e484f82fd92d7fd80b5a980afe82415ca38b"}, {"file": "apps/recruitment/create-job/components/CreateJob/InnerScreen.tsx", "hash": "0ceef0555713ce3ee44e62f0e2dd115cf391a7dd", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/JobCreationChatBot/ActionProvider.tsx", "hash": "d8942254d4d4b07d57b4baf5581664b27fae62cc"}, {"file": "apps/recruitment/create-job/components/CreateJob/JobCreationChatBot/actionUtils.ts", "hash": "e3d925e901aba323b19cff9af47f854a8cdca16d", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/create-job/components/CreateJob/JobCreationChatBot/config.ts", "hash": "79eafff577ab675479342bd0b74911e17edb6510"}, {"file": "apps/recruitment/create-job/components/CreateJob/JobCreationChatBot/CutomComponents/BotAvatar.tsx", "hash": "52bdb5afa3f0ad0fe54f05a8b88c14e84856f8ad"}, {"file": "apps/recruitment/create-job/components/CreateJob/JobCreationChatBot/CutomComponents/ChatHeader.tsx", "hash": "0250fc1da396b9176dded9989a706371c081ceb1"}, {"file": "apps/recruitment/create-job/components/CreateJob/JobCreationChatBot/JobCreationChatbot.tsx", "hash": "54a29983150a275ff6ea9c9ef13cea7894b80af6"}, {"file": "apps/recruitment/create-job/components/CreateJob/JobCreationChatBot/MessageParser.tsx", "hash": "325ecad6c9a4fd12d37c24f7df050d2d51c20d64"}, {"file": "apps/recruitment/create-job/components/CreateJob/JobHead.tsx", "hash": "aad86498f13166402a8fa875dbc48d42e18b60ba", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/MultiSelect/MultiSelect.tsx", "hash": "2315d72f18bdf43d4b67ecc2ca6d289358e85725", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/MultiSelect/MultiSelectHeadItem.tsx", "hash": "ab6c56f1f3ccb6fd219422f418127be3a00df357"}, {"file": "apps/recruitment/create-job/components/CreateJob/MultiSelect/MultiSelectListItem.tsx", "hash": "51a9b8575f8bb9986306d684d9d42128f354e628"}, {"file": "apps/recruitment/create-job/components/CreateJob/Popups/ApprovalPopup.tsx", "hash": "6c72577f59c55a3b6a39d0350a1d2f2a4872753d", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/Popups/CancelPopup.tsx", "hash": "32ab4d4c76a0e0edde4d51fdda491bf049579873"}, {"file": "apps/recruitment/create-job/components/CreateJob/Popups/ChangeSaved.tsx", "hash": "1b2f50f11836d683777ad6782a0a55ed3fe4ddd2"}, {"file": "apps/recruitment/create-job/components/CreateJob/Popups/DraftPopup.tsx", "hash": "a5a83ddc159a3e99c378779371915d545a5d8923", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/Popups/JobTargetPopup.tsx", "hash": "c4b5cc85d18da8d12c8b3ab8c8c23f825a171a76", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/Popups/PublishPopup.tsx", "hash": "b72cc8fbf39792e911660ab9ecd197929752d263", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/Popups/SaveTemplatePopup.tsx", "hash": "2e0634bef5f4daf922f8ce4a1c91bc48b301e4b7"}, {"file": "apps/recruitment/create-job/components/CreateJob/Progress.tsx", "hash": "53808574c213af004896136130211ad647c60082"}, {"file": "apps/recruitment/create-job/components/CreateJob/setDataHoc.tsx", "hash": "85dd514bee6843d2f49d10c5933dae1c227bc2eb", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/CreateJob/SimpleQuestions/SimpleQuestionItem.tsx", "hash": "e6e2edb30d54fad1a25317432135696718fa73a7"}, {"file": "apps/recruitment/create-job/components/CreateJob/SimpleQuestions/SimpleQuestions.tsx", "hash": "4f7423bb6a0110c1131c1d1a258f4bf68466a824"}, {"file": "apps/recruitment/create-job/components/CreateJob/Steps/AboutCompany.tsx", "hash": "7113317a635ef52414bbe4d978fbcf44f59efdfd", "deps": ["api", "recruitments"]}, {"file": "apps/recruitment/create-job/components/CreateJob/Steps/ApplicationForm.tsx", "hash": "6e4a9f5ecf3713dc20681ab27787cc41dfbba2d1"}, {"file": "apps/recruitment/create-job/components/CreateJob/Steps/Benefits.tsx", "hash": "e11024b8f0c40e65c7cd2466dc6c0fb56a9828c2"}, {"file": "apps/recruitment/create-job/components/CreateJob/Steps/JobBoards.tsx", "hash": "0b5727e87c954d6653715c038a78ed175705ad5e"}, {"file": "apps/recruitment/create-job/components/CreateJob/Steps/JobDetails.tsx", "hash": "22efa2f2c4e8882e9e196f3763c16c34ccbfc3ea", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/create-job/components/CreateJob/Steps/Requirements.tsx", "hash": "7ef83ed4aa3a17ed004b7a87d3ef409d58f5d2d4", "deps": ["api"]}, {"file": "apps/recruitment/create-job/components/CreateJob/Steps/SalaryRange.tsx", "hash": "bd761d6a5902e8a7463c1fb73d596abc30f5f990"}, {"file": "apps/recruitment/create-job/components/EmptyJob.tsx", "hash": "b898243331ed1bfa0b8db56e9488c73d127f82bc"}, {"file": "apps/recruitment/create-job/components/RichTextEditor.tsx", "hash": "c2f9f1526514aa3074d7e92c099e5d6e931012b8"}, {"file": "apps/recruitment/create-job/components/SingleSelectOptionWorkflows.tsx", "hash": "97d7e67144779ebc56128f79d3e9f225c5a1ac54"}, {"file": "apps/recruitment/create-job/components/ViewJob/JobAction.tsx", "hash": "51a78647704eaa7f00486c02bc75903649c3b209", "deps": ["api", "recruitments", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/ViewJob/JobInformation.tsx", "hash": "3e697f86f59aa0d2a37b0141c71ce254885db6a0", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/components/ViewJob/Popups/ApplyPopup.tsx", "hash": "555fa45e0f8665e109b84413ae25a16d090b4a7d"}, {"file": "apps/recruitment/create-job/components/ViewJob/Popups/SubscribePopup.tsx", "hash": "00065b5644ba6f1538b8162c1937c0016b4f0fd4"}, {"file": "apps/recruitment/create-job/components/ViewJob/Recommended.tsx", "hash": "5bb5d6c784ce069f79e653e610ea91ff7d542045"}, {"file": "apps/recruitment/create-job/hook/convertDataToString.ts", "hash": "6adffd7f3259f87fd548f5ae87209cc3ca93e012"}, {"file": "apps/recruitment/create-job/hook/deleteData.ts", "hash": "4bdc2e5ac8f20d8fc927551fde6de26e1c994494"}, {"file": "apps/recruitment/create-job/hook/fetchData.tsx", "hash": "944ce6a142634af71f52defe830cebe0bc9ecce8", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/hook/numberWithCommas.tsx", "hash": "bde4064662b852e23dbd66ed9eb1afbca664ef18"}, {"file": "apps/recruitment/create-job/hook/putData.tsx", "hash": "b22d5cd48354bd40831111e6629c22cfd903f9d3"}, {"file": "apps/recruitment/create-job/hook/rangeValidate.ts", "hash": "68efb50d58fe7327f82766129ed9127bc1505c4e"}, {"file": "apps/recruitment/create-job/hook/sbsPasswordCheckForErrors.ts", "hash": "6214335c73a9db2be6f0f092b6ced26f86eca434"}, {"file": "apps/recruitment/create-job/hook/selectSearchFunc.ts", "hash": "25408d0a8601450669ab1ddb1b4cb3d5ef801a9b", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitment/create-job/hook/useCurrentPopupData.tsx", "hash": "d20d7b02f0bec39b1e7fde40efa05e9aa2d20a83"}, {"file": "apps/recruitment/create-job/hook/useTypedSelector.ts", "hash": "f4ab5ebd971ece2047763ab52b920933fe7e72ec"}, {"file": "apps/recruitment/create-job/hook/validateEmail.ts", "hash": "b37f54df684a4f6ff4869afd98351fc48487b22b"}, {"file": "apps/recruitment/create-job/hook/validatePassword.ts", "hash": "b8d32d92894d9f36746a50b219b4af176fb4d26a"}, {"file": "apps/recruitment/create-job/hook/validatePhoneNumber.ts", "hash": "ddc48adf5d279933dc1b784b77eb77ead51b7e8d"}, {"file": "apps/recruitment/create-job/hook/validateUrl.ts", "hash": "9da1c69c87458de6de45cd0217e2f352d1a9b6b4"}, {"file": "apps/recruitment/create-job/image/empty-job.svg", "hash": "a3d413547483a81ff400e324690bfe04c0d3c7d3"}, {"file": "apps/recruitment/create-job/image/icon/aexpress_icon.svg", "hash": "1b40fb9500e7aeeeba72b9408ded476df17a07a4"}, {"file": "apps/recruitment/create-job/image/icon/alarm_image.svg", "hash": "4ea71179cd92515ed94c55cf01f85a979acfd57b"}, {"file": "apps/recruitment/create-job/image/icon/application_bool_ic.svg", "hash": "5ebd3f05e5b897f2a5d22dc1be3df1d777013820"}, {"file": "apps/recruitment/create-job/image/icon/application_checkbox_ic.svg", "hash": "0b088c07a240e1d1006f49762977e822dbf706fa"}, {"file": "apps/recruitment/create-job/image/icon/application_radio_ic.svg", "hash": "1244d7c0fbe896664339cfd3290bf3b34acaa617"}, {"file": "apps/recruitment/create-job/image/icon/application_text_ic.svg", "hash": "f5c86d46899360885fe4b9c013c6c024c84078b6"}, {"file": "apps/recruitment/create-job/image/icon/arrow_back_ic.svg", "hash": "68ecbd94243e850bc9fd4498c871140cf14c88af"}, {"file": "apps/recruitment/create-job/image/icon/assign-to-job_ic.svg", "hash": "82db94bec41a778beb6e3efc216ea7be311ffa53"}, {"file": "apps/recruitment/create-job/image/icon/avatar.svg", "hash": "65a2e59f894b3a07e5ccf314a937401487aeb2db"}, {"file": "apps/recruitment/create-job/image/icon/calendar_ic.svg", "hash": "05f6771e1475c792445c092090645cc6b0dfc105"}, {"file": "apps/recruitment/create-job/image/icon/camera_ic.svg", "hash": "460259fa3fad5b13b014b0a063da50950e1dad0b"}, {"file": "apps/recruitment/create-job/image/icon/chatbot.svg", "hash": "26486efcddd6ce537e90fde0c0efb28f2a043402"}, {"file": "apps/recruitment/create-job/image/icon/chose_ic_white.svg", "hash": "01d3064600b26730ef7ce69bda6154a488b1759c"}, {"file": "apps/recruitment/create-job/image/icon/chose_ic.svg", "hash": "6f1b1bbe960a9c4e265732020cb1416ee294fdf9"}, {"file": "apps/recruitment/create-job/image/icon/clock_ic.svg", "hash": "d3d68cbcc0a65ef112e40ffcbc14afd2bbbd3d98"}, {"file": "apps/recruitment/create-job/image/icon/close.svg", "hash": "ce25366d951f523c6f07d37840246aae8af924c2"}, {"file": "apps/recruitment/create-job/image/icon/copy-link_ic.svg", "hash": "e09ea42289056c85adf5ced60d5891649e11c5d6"}, {"file": "apps/recruitment/create-job/image/icon/delete_ic.svg", "hash": "9ca29516d488f6e4ebdb1a717a11938013003421"}, {"file": "apps/recruitment/create-job/image/icon/delete_image.svg", "hash": "87b631b5cb988d35da250e250c9f7a00f2d31955"}, {"file": "apps/recruitment/create-job/image/icon/diners_ic.svg", "hash": "54f3f5e60cda07e81d184226c2547a81e2cf04e6"}, {"file": "apps/recruitment/create-job/image/icon/discover_icon.svg", "hash": "89e82f5fe045e05073ab6c47c95d88cd39cf51fd"}, {"file": "apps/recruitment/create-job/image/icon/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/recruitment/create-job/image/icon/download_ic.svg", "hash": "9af7231119296b2e183f193514a821a1c206c88e"}, {"file": "apps/recruitment/create-job/image/icon/droppable_select_ic.svg", "hash": "476ee10947444b83119641781ed809ff02f5fa3f"}, {"file": "apps/recruitment/create-job/image/icon/edit_green_ic.svg", "hash": "c48cc19062d2ac41575c5c247835cca8c808706f"}, {"file": "apps/recruitment/create-job/image/icon/edit_ic.svg", "hash": "4da2e5d0dfc6642794db97fe3fbba3ea63e30c0a"}, {"file": "apps/recruitment/create-job/image/icon/eye_icon.svg", "hash": "13a3f027efff31127ef4f772d7d2b08c2eef23c7"}, {"file": "apps/recruitment/create-job/image/icon/facebook_ic_green.svg", "hash": "714924c98a54e86f0dda43353c0c2afb191cd378"}, {"file": "apps/recruitment/create-job/image/icon/facebook_ic.svg", "hash": "dd6aaa11d276571e57abf128d2a962430cb54ea9"}, {"file": "apps/recruitment/create-job/image/icon/filter-arrows.svg", "hash": "179f6a9fd6dc2b6221d3b86566e14e576f99b5a7"}, {"file": "apps/recruitment/create-job/image/icon/form-arrow_ic.svg", "hash": "7c79ca85c67951bbd9379a2de97ccf3ed6a8c2dc"}, {"file": "apps/recruitment/create-job/image/icon/globe.svg", "hash": "32587f3bbb9b57110f02cbf3e2f226d315860526"}, {"file": "apps/recruitment/create-job/image/icon/green_message_ic.svg", "hash": "9cba2c1b117b3337b60d93c1f61f1b8f42bf66d1"}, {"file": "apps/recruitment/create-job/image/icon/green_plus_icon.svg", "hash": "b171de093654f025ac14c43c5d77f3ee96a8fcf6"}, {"file": "apps/recruitment/create-job/image/icon/info_ic.svg", "hash": "2a47846f2e17314c5991014317880ccb2ded00db"}, {"file": "apps/recruitment/create-job/image/icon/instagram_ic_green.svg", "hash": "806bdbf9548058015054552f595a018c85314ad8"}, {"file": "apps/recruitment/create-job/image/icon/instagram_ic.svg", "hash": "ec7ea99805cfe319afc38782d5f592187bac516a"}, {"file": "apps/recruitment/create-job/image/icon/jcb_ic.svg", "hash": "a6e404c10decdc77ae5026656d5b94bb9b091f39"}, {"file": "apps/recruitment/create-job/image/icon/linkedIn_ic_green.svg", "hash": "10c27ebca8fe91d820a7db49c30f1bdf7e8ecb84"}, {"file": "apps/recruitment/create-job/image/icon/linkedIn_ic.svg", "hash": "6c10f6857f4125a930eb5060d048044dc86fce3f"}, {"file": "apps/recruitment/create-job/image/icon/mastercard_ic.svg", "hash": "6455928e9bca8360648c1aba84b822393ab8e944"}, {"file": "apps/recruitment/create-job/image/icon/mastercard_small_ic.svg", "hash": "438b41e267cd941942ae6b1f09d9c7e08801ff17"}, {"file": "apps/recruitment/create-job/image/icon/paper_clip_ic.svg", "hash": "c6608cd04f99211f58b1082a4953151bca3fc098"}, {"file": "apps/recruitment/create-job/image/icon/paperclip.svg", "hash": "37ae71db143f75fb1794c3ab1a2c9cae9f11c9da"}, {"file": "apps/recruitment/create-job/image/icon/plus_ic.svg", "hash": "cd242dafa9819a62487dac97e59122449808d6e0"}, {"file": "apps/recruitment/create-job/image/icon/red_edit_ic.svg", "hash": "3176d977556515fb00054fd18184a9be44de6769"}, {"file": "apps/recruitment/create-job/image/icon/search_ic.svg", "hash": "885b2d6b457b5b51f2d2823e7da7d47fbd899164"}, {"file": "apps/recruitment/create-job/image/icon/share.svg", "hash": "c7e1156bc5e6a995a6abed6d6826368f6375eaf8"}, {"file": "apps/recruitment/create-job/image/icon/small_done_ic.svg", "hash": "8d1a6c4d06667a0bdddb5e33a7ebd211d68ab3b6"}, {"file": "apps/recruitment/create-job/image/icon/small-arrow-down_ic.svg", "hash": "cd0c507ded5bbc4bd211c189ecb4e427bf328889"}, {"file": "apps/recruitment/create-job/image/icon/success_image.svg", "hash": "524f030321ec0c5779e19acf20e0d13f05263535"}, {"file": "apps/recruitment/create-job/image/icon/twitter_ic_green.svg", "hash": "a7030e919b908d30dc085bc622dc43b79153c68f"}, {"file": "apps/recruitment/create-job/image/icon/twitter_ic.svg", "hash": "3587f9727842f0d8ac3bf968420dd8f26bb1ca5b"}, {"file": "apps/recruitment/create-job/image/icon/union_icon.svg", "hash": "50118bdac983b07097af8e9220f6adc965fd6aa7"}, {"file": "apps/recruitment/create-job/image/icon/upload-cloud_ic.svg", "hash": "6d791d28cffd16a3b64964a42d5dd06fb027de1b"}, {"file": "apps/recruitment/create-job/image/icon/visa_black_ic.svg", "hash": "b51c4929c24dab2815df62b312c7a21d53768f52"}, {"file": "apps/recruitment/create-job/image/icon/visa_ic.svg", "hash": "a1c804c6a728ea4d4e092e33da6b1050926e3de5"}, {"file": "apps/recruitment/create-job/image/icon/visa_small_ic.svg", "hash": "90c619e3b3fdf642f84370e36775ce7b0cefa1e4"}, {"file": "apps/recruitment/create-job/image/tempUser.png", "hash": "ed3f40d4a392ad37fbad77f05a1c2ff16199c64c"}, {"file": "apps/recruitment/create-job/image/user.png", "hash": "185071bb5b890a00d4210d03587ff84fab0f19eb"}, {"file": "apps/recruitment/create-job/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/recruitment/create-job/package.json", "hash": "1f77e30597d2992cbffc7e88f7666ad1f477f081", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/recruitment/create-job/screens/ViewJobScreen.tsx", "hash": "22593c0b2f072dbcacd83cc2413d5b35a4c5d9a0", "deps": ["api", "recruitments", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/recruitment/create-job/src/root.component.tsx", "hash": "434ef36a4850c9780e2384ff170fb05a8b38b21e", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/src/urecruits-create-job.tsx", "hash": "23e936c0a9916f30e17053d6e8b5a5a7f0c1d4f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/staticData/positionData.ts", "hash": "e73dbc8edb7ff7a8e11c133ed02c4939f7b37df8"}, {"file": "apps/recruitment/create-job/store/action-creators.ts", "hash": "d12c6a0bb858b46aecf7825ba9d0d5e81b8b6fdb"}, {"file": "apps/recruitment/create-job/store/apply-action-creators.ts", "hash": "840dc60c60d55ab1c66b9e1af9d080ddfbf9cddc"}, {"file": "apps/recruitment/create-job/store/apply-redux-enums.ts", "hash": "4e6cbf06f91c3939b1e5bd3f161cbe41ba2eb46f"}, {"file": "apps/recruitment/create-job/store/index.ts", "hash": "642c52f8299f6ca0e44938ad75171d8d8bb6e7f8"}, {"file": "apps/recruitment/create-job/store/reducers/ApplyReducer.ts", "hash": "a076535d5ff1bd4f594d89060cfe0d3e044a5265"}, {"file": "apps/recruitment/create-job/store/reducers/chatJobReducer.ts", "hash": "daf674079629a42a0e853d92f3bc8a3721910b38"}, {"file": "apps/recruitment/create-job/store/reducers/index.ts", "hash": "fd04cb923fdbbf77aedd61eba4e31c813d6036ac"}, {"file": "apps/recruitment/create-job/store/reducers/screenReducer.ts", "hash": "a3efd6d9efe20425609d2fa8f850fbae89a0507c"}, {"file": "apps/recruitment/create-job/store/redux-enums.ts", "hash": "fea806f5dc587cff9e101b5ccc67902ec0256ddc"}, {"file": "apps/recruitment/create-job/styles/_application_form.scss", "hash": "5129197dfb970d9a4294b563029bfbd80388fa6e"}, {"file": "apps/recruitment/create-job/styles/_config.scss", "hash": "ca498c144478ff5719d67cb11811ecbcb3d95102"}, {"file": "apps/recruitment/create-job/styles/_custom-phone-input.scss", "hash": "38839746226fa485b7b22e05f58b1585b442d802"}, {"file": "apps/recruitment/create-job/styles/_empty-job.scss", "hash": "908d0ac6927623ad70d4f42bd52cd11077123a49"}, {"file": "apps/recruitment/create-job/styles/_head.scss", "hash": "a73dabd6004954700c1eb115033319801168736e"}, {"file": "apps/recruitment/create-job/styles/_job-action.scss", "hash": "81faa10cb1f1625e71fdb5000a549544b3230eba"}, {"file": "apps/recruitment/create-job/styles/_job-boards.scss", "hash": "13c1e174a9826de49fe85e6f7985ad0a49302539"}, {"file": "apps/recruitment/create-job/styles/_job-creation-chatbot.scss", "hash": "84c11c3a11cdadaf8539afe96333504dea3aab5f"}, {"file": "apps/recruitment/create-job/styles/_job-information.scss", "hash": "7741a37feb902ba85182a2ceebc0002e49c60d54"}, {"file": "apps/recruitment/create-job/styles/_job.scss", "hash": "a8d86a4b9b0245c3c122ba329f3830dce0810b1f"}, {"file": "apps/recruitment/create-job/styles/_loader.scss", "hash": "d0a239563c630323df5f128ee3f77ffd283a624c"}, {"file": "apps/recruitment/create-job/styles/_mixins.scss", "hash": "4ad91cf2934ef23510945ec73e4ffd440ec2a9ad"}, {"file": "apps/recruitment/create-job/styles/_multi-select.scss", "hash": "f90b423042aa3c0261ef9cbd034ac86c935de2a8"}, {"file": "apps/recruitment/create-job/styles/_popups.scss", "hash": "599a66d930284656294990655e65d2e05b54de9c"}, {"file": "apps/recruitment/create-job/styles/_progress.scss", "hash": "d8d961a4e8fdc26515709a851bb242d286d30474"}, {"file": "apps/recruitment/create-job/styles/_react-quill.scss", "hash": "2af03491174f51752bee8c91005274ac57bd92d5"}, {"file": "apps/recruitment/create-job/styles/_recommended.scss", "hash": "70d0e9c95c980cd16ecef69f99e8d5870cef0636"}, {"file": "apps/recruitment/create-job/styles/_simple-questions.scss", "hash": "b25a4a24fdfcf5e50dce93456f2b1c3baf5fe7b1"}, {"file": "apps/recruitment/create-job/styles/_small-loader.scss", "hash": "c9a321e845d13b7c7619e041785c7271aa206386"}, {"file": "apps/recruitment/create-job/styles/_switcher.scss", "hash": "222d25248e05879f5b18aff217ddba494e0aa3b8"}, {"file": "apps/recruitment/create-job/styles/datepicker/datepicker.scss", "hash": "4142654d4891dc36dd6ef1426c27d95f73d74864"}, {"file": "apps/recruitment/create-job/styles/datepicker/mixins.scss", "hash": "255978e34dbc387795d3b9cda56a2f629c61390c"}, {"file": "apps/recruitment/create-job/styles/datepicker/variables.scss", "hash": "48109e34218223e6e229f60f29339fea9a17b78e"}, {"file": "apps/recruitment/create-job/styles/job-detail.scss", "hash": "7877e5c94c459cbcc6422fe7b29a61b68f5149b8"}, {"file": "apps/recruitment/create-job/styles/main.scss", "hash": "bae339b81b456a8658bd74ea5a83409151977859"}, {"file": "apps/recruitment/create-job/styles/range.scss", "hash": "5c92cc4499326e6fe3751072ae9b3d552b07d1d5"}, {"file": "apps/recruitment/create-job/styles/selectCustomErrorStyle.ts", "hash": "1de06be5c8a2e8e2895788ce9c258d419e7ecb87"}, {"file": "apps/recruitment/create-job/styles/selectCustomStyle.ts", "hash": "631040ffab2e6de8a025b61521ad8083f01e9872"}, {"file": "apps/recruitment/create-job/styles/selectDisableStyle.ts", "hash": "118b5a21cd0ecc90b4789162d4e857ec01cde76a"}, {"file": "apps/recruitment/create-job/styles/selectMobileMenuStyle.ts", "hash": "13ff3678aeb4d0ec44aa3867b15621f89fedeb03"}, {"file": "apps/recruitment/create-job/styles/selectSmallStyle.ts", "hash": "147446a4e98f42fe6c7b0b1e786d6dae53fadf01"}, {"file": "apps/recruitment/create-job/tsconfig.json", "hash": "79915f1d475a0c20e60eef92f1dd6ec9c6e0665c"}, {"file": "apps/recruitment/create-job/types/global.ts", "hash": "6d489fd751e60fa7e0a8b4835f6d6966f67fae32"}, {"file": "apps/recruitment/create-job/types/redux-chat-job-details.ts", "hash": "90b4a558e9be3c6a1c097a1a62a7daba3ead9e7b"}, {"file": "apps/recruitment/create-job/types/redux-types-apply-job.ts", "hash": "3fde200278d7a12f0cc51d0eb4c4dc983dba15b0"}, {"file": "apps/recruitment/create-job/types/redux-types.ts", "hash": "b3dde98c4fbcc4832b3c724ec3efa725d9d0ee45"}, {"file": "apps/recruitment/create-job/types/view-job.ts", "hash": "a484b2f92f2a2989f6c54977bfe99df24e50e1a9"}, {"file": "apps/recruitment/create-job/util/constants.ts", "hash": "755846f741abd7eafb8e692f8eeb0d093de048d4"}, {"file": "apps/recruitment/create-job/util/firebase.js", "hash": "41eb542b58303b6d4f8915d32a067ef54ed5c3d2"}, {"file": "apps/recruitment/create-job/util/requestNotification.ts", "hash": "c4b8ced217078567c321db1c98a34bc43a5202c3", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitment/create-job/webpack.config.js", "hash": "313451287849aa3133a5a6aee01157fbf9fd7c33"}]}}, "hr-analytics": {"name": "hr-analytics", "type": "lib", "data": {"root": "apps/hr-analytics", "sourceRoot": "apps/hr-analytics", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/hr-analytics/.eslintrc", "hash": "c1c13f5b318fdf9c0f72ac6e5ef4a268b286dc69"}, {"file": "apps/hr-analytics/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/hr-analytics/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/hr-analytics/api/fetchDataApi.ts", "hash": "2063a69b93e36b9ec70c3bf60ee4275ead22ce7c"}, {"file": "apps/hr-analytics/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/hr-analytics/components/DetailedViewJob/MatchedProfile/MatchedJobFilter.tsx", "hash": "4caf46691417357113e4b51e6280eaaea687f08b", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/components/DetailedViewJob/MatchedProfile/MatchedProfileTBody.tsx", "hash": "475285eafb97c8235067d025cf74d801eff432c1"}, {"file": "apps/hr-analytics/components/DetailedViewJob/MatchedProfile/MobileTableDetailedView.tsx", "hash": "c7b9625e2b3c5a0039d3cce6c06bb8c8ff71617d"}, {"file": "apps/hr-analytics/components/DropDown/DropDown.tsx", "hash": "9ec406b47d103b2433ef4960000683454d379c87"}, {"file": "apps/hr-analytics/components/FileUploadProgress/FileUploadProgressBar.tsx", "hash": "ba97d6deab14bae1847e15644e3816c4f6f55fa3"}, {"file": "apps/hr-analytics/components/Global/MultySelectSkills/MultiSelect.tsx", "hash": "305c6dc166417e0afdd955a97821a192d86c2719", "deps": ["api"]}, {"file": "apps/hr-analytics/components/Global/MultySelectSkills/MultiSelectListItem.tsx", "hash": "e257d3846ea599c694ae79d6df009b45d520eda8"}, {"file": "apps/hr-analytics/components/Global/SelectOptions.tsx", "hash": "36475a490021364203bc15f176bf16935f0f7f60"}, {"file": "apps/hr-analytics/components/Global/table/FilterWrapper.tsx", "hash": "967bb3a80ad6cd75e7fd49b6b659fac1605863b4"}, {"file": "apps/hr-analytics/components/Global/table/MemoOrderSVG.tsx", "hash": "10f534a5267771141100610d9ed94fb22ee7da72"}, {"file": "apps/hr-analytics/components/Global/table/NoResultsSearch.tsx", "hash": "0c72a5954d9c83ea2e77884071ee18905c217b1c"}, {"file": "apps/hr-analytics/components/Global/table/SingleMobileItem.tsx", "hash": "01b9977be776a6baf6fbcae396df482aee9d5be7"}, {"file": "apps/hr-analytics/components/Global/table/TableCheckbox.tsx", "hash": "8cdd87fa5def0e80381b6a0647ebfcd991ca21df"}, {"file": "apps/hr-analytics/components/Global/table/TableEmpty.tsx", "hash": "bd4035688d4992bc69b1410b3dcf4470c464ec19"}, {"file": "apps/hr-analytics/components/Global/table/TopFilterButton.tsx", "hash": "099cc9e7bf1117e3085228f1c14ee585a88130ab"}, {"file": "apps/hr-analytics/components/Jobs/JobsFilterItems.tsx", "hash": "32532b49297f140456cce87aa87e95c9685b59c8"}, {"file": "apps/hr-analytics/components/Jobs/popups/ApproveJobPopup.tsx", "hash": "598bc9adf931e920a80cf54f9ea0f21a2bebfee5", "deps": ["api"]}, {"file": "apps/hr-analytics/components/Jobs/popups/ChangeWorkflowPopup.tsx", "hash": "4d0af0ce4e976ed97973a4b9437a0982fbb042a3", "deps": ["api"]}, {"file": "apps/hr-analytics/components/Jobs/popups/DeletePopupJobs.tsx", "hash": "b95c00cedd5b170b5ffa08ff7b5ab2883f3d2044", "deps": ["api"]}, {"file": "apps/hr-analytics/components/Jobs/popups/RejectPopupJobs.tsx", "hash": "06fd515b52f8338b2f8452ef0b2c6e0b2d93d760", "deps": ["api"]}, {"file": "apps/hr-analytics/components/Jobs/TBody/JobsTBody.tsx", "hash": "663b27318829f1e3abe686ca6722fd7328f5235c", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/components/Jobs/TBody/MobileTableJobs.tsx", "hash": "cbd527751c0c54718087d3b0675d86f8af8e5e16"}, {"file": "apps/hr-analytics/components/Jobs/TBody/ReactSelectWorkflowsJobTable.tsx", "hash": "1246deb6329bff8e2937e871ef7efca88ddf0922", "deps": ["recruitments"]}, {"file": "apps/hr-analytics/components/Jobs/TBody/SelectStatus.tsx", "hash": "3f2cba57ddf65a6477578fad792c5dcc7b22541b", "deps": ["api"]}, {"file": "apps/hr-analytics/components/Loader/Loader.tsx", "hash": "1d7212fd1bd6fad123f9ee9f639fb6a540b913d5"}, {"file": "apps/hr-analytics/components/Report/AnalyticsEntryCard.tsx", "hash": "ae976f49ee97483c95e9247f332e51473b7923a9"}, {"file": "apps/hr-analytics/components/Report/Assessments/Filters/FiltersAssessmentsAnalytics.tsx", "hash": "3964881a6d426bed766264b1e5ce1dc2e2c4fc8a", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/components/Report/Chart/AreaChart.tsx", "hash": "de3e174f6c151fdce947022c54b94e4276166500"}, {"file": "apps/hr-analytics/components/Report/Chart/LineChart.tsx", "hash": "623327fe095b15c51fdfd0319bcec4329060b48b"}, {"file": "apps/hr-analytics/components/Report/Members/CircleChartMembersAge.tsx", "hash": "****************************************"}, {"file": "apps/hr-analytics/components/Report/Members/Filters/FiltersMembersAnalytics.tsx", "hash": "55bc4146ca6c7aff683928825c446831ce198ea1", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/components/Table/MailBoxTable.tsx", "hash": "fc464f9da04acb0992a579309e2071d8064e94e3", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/components/Table/S3StorageTable.tsx", "hash": "09c83cadeded1e8b30a67e2f14aab8caadff3424"}, {"file": "apps/hr-analytics/enums/assessments/assessmentsEnums.ts", "hash": "790d18af0a2a3629385639b69dfb6f9d37257ad5"}, {"file": "apps/hr-analytics/enums/detail-view-job/detail-view-job-matched.ts", "hash": "4de0280a01ff9b4b89ceeedf86275ad93029c1eb"}, {"file": "apps/hr-analytics/enums/jobs/jobsEnums.ts", "hash": "c438ff736b33cf3a09183ce0356c56430dc93b9c"}, {"file": "apps/hr-analytics/enums/members/membersEnums.ts", "hash": "9eef9721431a4df2377c7c8a6a2b1feb4beda191"}, {"file": "apps/hr-analytics/hook/fetchData.ts", "hash": "5506d463506eef655f412a440c72cf4713039257", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/hook/useClickOutside.ts", "hash": "7fe6045f7460948ae1e0816013bc5f1e8c449321"}, {"file": "apps/hr-analytics/hook/useTableClickAndDragScroll.tsx", "hash": "b186aee26af74a8a4ebb4d606e8b763e1ae9ea17"}, {"file": "apps/hr-analytics/hook/validateEmail.ts", "hash": "8e558e101fe4d056b15d28cd46fffc21bfea11eb"}, {"file": "apps/hr-analytics/hook/validatePhoneNumber.ts", "hash": "e7e1e97fa068915a022144259f4e76abe6bfe570"}, {"file": "apps/hr-analytics/image/document.svg", "hash": "1d4ad708980bc967b9888866781eae5cf258ce8e"}, {"file": "apps/hr-analytics/image/greeting_img.svg", "hash": "bd6a1c0c0992027d46dc7ae6ec5603bbd40a5475"}, {"file": "apps/hr-analytics/image/Group_455.svg", "hash": "ed8deb67686ca7286728584d161aba4fd3a7dee5"}, {"file": "apps/hr-analytics/image/icon/approve_job_ic.svg", "hash": "88019e070c7a267a3e16aa7b6eb166179269804c"}, {"file": "apps/hr-analytics/image/icon/avatar.svg", "hash": "a6dfdace3013cadec974a9b5e89b193268aa4c0a"}, {"file": "apps/hr-analytics/image/icon/book-open.svg", "hash": "ad0fbbff04aff34ff632e8d8b03ed2071936dcaf"}, {"file": "apps/hr-analytics/image/icon/calendar_ic.svg", "hash": "05f6771e1475c792445c092090645cc6b0dfc105"}, {"file": "apps/hr-analytics/image/icon/chat_candidate_profile.svg", "hash": "92c478f153212d49e9521ba51eacd99dab7c4f22"}, {"file": "apps/hr-analytics/image/icon/clock_ic.svg", "hash": "2d08f33c20f0cff1f940bd2d11790c77dadeabf6"}, {"file": "apps/hr-analytics/image/icon/Close.svg", "hash": "52fdcbd8986403fb52049057bf07378467013b06"}, {"file": "apps/hr-analytics/image/icon/cross.svg", "hash": "746d1ad7cba2c65ad9d9da725304e2bb67dc3128"}, {"file": "apps/hr-analytics/image/icon/cw_ic.svg", "hash": "8319ddcd7ddfd9b8deb8fc32c7dc54f6224373de"}, {"file": "apps/hr-analytics/image/icon/delete_ic.svg", "hash": "9ca29516d488f6e4ebdb1a717a11938013003421"}, {"file": "apps/hr-analytics/image/icon/delete_image_popup_ic.svg", "hash": "0b7567b7592f9d3280b705c6a2b9ccb07fdc9705"}, {"file": "apps/hr-analytics/image/icon/done__inactive_ic.svg", "hash": "6b4094faa42c733d61c02a58cdabbc67331041e6"}, {"file": "apps/hr-analytics/image/icon/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/hr-analytics/image/icon/download_ic.svg", "hash": "9af7231119296b2e183f193514a821a1c206c88e"}, {"file": "apps/hr-analytics/image/icon/dropdown_down.svg", "hash": "5c9c7bff3033da6a05283ceaaef4becbb44390e4"}, {"file": "apps/hr-analytics/image/icon/dropdown_up.svg", "hash": "1d811b887cce2adb064e1178a252637ecfa186b7"}, {"file": "apps/hr-analytics/image/icon/edit_ic.svg", "hash": "5f3b472b990252dbffb690826248bda2045ef414"}, {"file": "apps/hr-analytics/image/icon/email_candidate_profile_ic.svg", "hash": "24ac72fa3baee43001e1ba9eb755c3c475713eec"}, {"file": "apps/hr-analytics/image/icon/email_candidate.svg", "hash": "e69f06c04dfbe897e1f73814ef85fa40aa266512"}, {"file": "apps/hr-analytics/image/icon/eye-on.svg", "hash": "48ee0ccc0d2630ad92ba15e71d8b57ce95a45dbb"}, {"file": "apps/hr-analytics/image/icon/form-arrow_ic.svg", "hash": "7c79ca85c67951bbd9379a2de97ccf3ed6a8c2dc"}, {"file": "apps/hr-analytics/image/icon/info_ic.svg", "hash": "2a47846f2e17314c5991014317880ccb2ded00db"}, {"file": "apps/hr-analytics/image/icon/magnet.svg", "hash": "97bfebe528f3f76fbda4356b16b556d9d0edd48e"}, {"file": "apps/hr-analytics/image/icon/match.svg", "hash": "e532370aa48a696cb1d227bead65169073577acb"}, {"file": "apps/hr-analytics/image/icon/members_profile_ic.svg", "hash": "56d2ff8617fb58f64a9e1a33aca94292227b7723"}, {"file": "apps/hr-analytics/image/icon/message.svg", "hash": "5d455de072382f402f3413eafaf57b3049b610f4"}, {"file": "apps/hr-analytics/image/icon/phone_candidate_profile_ic.svg", "hash": "7f3d896cc2512804921f33aaa64954472e730753"}, {"file": "apps/hr-analytics/image/icon/plus_ic.svg", "hash": "cd242dafa9819a62487dac97e59122449808d6e0"}, {"file": "apps/hr-analytics/image/icon/reject_job_ic.svg", "hash": "8a7dee66d7b908222401c701770e83ea95802ac2"}, {"file": "apps/hr-analytics/image/icon/resent invite_ic.svg", "hash": "1728674f1046142420dfebab1641f7239e7ab031"}, {"file": "apps/hr-analytics/image/icon/reset_ic.svg", "hash": "f7384a5d57ac044338e4ec8b7ed088ec38f2c952"}, {"file": "apps/hr-analytics/image/icon/save_ic.svg", "hash": "ea61d1da58599e4dd91a8991d378ac396226cb58"}, {"file": "apps/hr-analytics/image/icon/send_ic.svg", "hash": "53ff60f040cee3d2d0238bee6d8ed6c63fd75be4"}, {"file": "apps/hr-analytics/image/icon/star_ic.svg", "hash": "267a8a19a44ea59d9fb48f3edddb29f0e6f66a03"}, {"file": "apps/hr-analytics/image/icon/table/empty-table.svg", "hash": "67f8903755aa519965e3fa11bcdff2d832453852"}, {"file": "apps/hr-analytics/image/icon/table/no-results.svg", "hash": "f3f308f1b83e66d88b296a62eee0aec610ac4422"}, {"file": "apps/hr-analytics/image/icon/tick.svg", "hash": "4516088f928ef310795170a86a2a714c08952b9a"}, {"file": "apps/hr-analytics/image/icon/Vector.png", "hash": "b5bf06ba02c98aa95836a5d182c4156a24213f7d"}, {"file": "apps/hr-analytics/image/icon/video_candidate_profile.svg", "hash": "3395d3f41db06603f8e7b2fdfba5e0784f253f55"}, {"file": "apps/hr-analytics/image/icon/workflow_add.svg", "hash": "4f18d367474d4d8127b5e154c3a5360cf8d0abf4"}, {"file": "apps/hr-analytics/image/no-data.svg", "hash": "b7baa1a754f35557402af798220062bf946aa702"}, {"file": "apps/hr-analytics/image/temp-user.png", "hash": "16418549d13ec89d7053b61c10fb42fe1de04e2d"}, {"file": "apps/hr-analytics/image/Vector.svg", "hash": "68834b9d50062fbf408649f8d5c569974a574218"}, {"file": "apps/hr-analytics/image/view-svgrepo-com.svg", "hash": "4c016b486d5bc258c5bf93ac6d1fa83c747cb267"}, {"file": "apps/hr-analytics/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/hr-analytics/package.json", "hash": "ffd739260644bfd018968c6bbae576dd4508d81e", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/hr-analytics/screen/AssessmentReportScreen.tsx", "hash": "f22ab644ea6769ccbfe1e3015db8bf7b688a07b0", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/hr-analytics/screen/HelloWorld.tsx", "hash": "28a7686027945207cfb0ed454393bcd3e3fb9d49"}, {"file": "apps/hr-analytics/screen/JobsReportScreen.tsx", "hash": "b7a73b41ffda7bd432b2d0f13a32bb77fee5ca49", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/hr-analytics/screen/JobsScreen.tsx", "hash": "a2e4b5c6e85a6a9deeb0e92650c49cb0575c74af", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/hr-analytics/screen/MembersReportScreen.tsx", "hash": "a44e41369c15af52e12b669e3c464e5a1253e116", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/hr-analytics/screen/ReportScreen.tsx", "hash": "de97e9a079ba0feacc12df35d53ac15b36583bbc"}, {"file": "apps/hr-analytics/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/hr-analytics/src/root.component.test.tsx", "hash": "ef1e9ce9aa95a15bdc7d000d4db1acccba108fdd"}, {"file": "apps/hr-analytics/src/root.component.tsx", "hash": "e9bf71ade1f11929741e7bc5e73682a137896c3b", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/src/urecruits-hr-analytics.tsx", "hash": "306467851bf40d77a958d22d63d9992e5d67c9f2", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/hr-analytics/store/index.ts", "hash": "8345c5fd5e6b0b53d288684c0a258143efd395b1"}, {"file": "apps/hr-analytics/store/reducers/assessments/assessmentDetailViewReducerArea.tsx", "hash": "2299b4369c983be25eb9d699766e70f94f1d3916"}, {"file": "apps/hr-analytics/store/reducers/assessments/assessmentsStatusPieReducer.tsx", "hash": "fe55e10551240fdb4dff7612a754d7d255460cb6"}, {"file": "apps/hr-analytics/store/reducers/assessments/assessmentsTableReducer.ts", "hash": "e47464dd87aef38cc7b87b96983d51ad4d695b3a"}, {"file": "apps/hr-analytics/store/reducers/jobDetailViewReducerArea.tsx", "hash": "70619c61237a3b6e92be50d9e51f7ceebf5c0bd5"}, {"file": "apps/hr-analytics/store/reducers/jobDetailViewReducerMatched.tsx", "hash": "eb0b7c2b846fa00331ec704a588914459334e8fc"}, {"file": "apps/hr-analytics/store/reducers/jobDetailViewReducerPie.tsx", "hash": "96dd28c9ec4d66df5b61979e6add243d3fcb4505"}, {"file": "apps/hr-analytics/store/reducers/jobsTableReducer.ts", "hash": "1c9202340b4a929e999bada3acca4f39212cd65d"}, {"file": "apps/hr-analytics/store/reducers/members/MembersAgeBreakdownReducerPie.tsx", "hash": "7738ec527b25d157d0742cb1ae9d6ac3d9858b38"}, {"file": "apps/hr-analytics/store/reducers/members/membersTableReducer.tsx", "hash": "3c6a20b996d504101aabb85f6298faaad60e68da"}, {"file": "apps/hr-analytics/styles/_analytics-entry-card.scss", "hash": "c2c6246b4c8b637d90142d7e4a98cb80a8ae5d1a"}, {"file": "apps/hr-analytics/styles/_area-chart.scss", "hash": "17be5c05f564b6568048a21246318043193f145b"}, {"file": "apps/hr-analytics/styles/_assessments-report.scss", "hash": "41e9062db5c17a93892f560282a1ab20ff4ebe71"}, {"file": "apps/hr-analytics/styles/_circle-chart.scss", "hash": "****************************************"}, {"file": "apps/hr-analytics/styles/_config.scss", "hash": "ca498c144478ff5719d67cb11811ecbcb3d95102"}, {"file": "apps/hr-analytics/styles/_dashboard.scss", "hash": "0992d5eb78bc5bc4620e573ba723b0e9940ba366"}, {"file": "apps/hr-analytics/styles/_hr-analytics.scss", "hash": "1ec7966cd5330f36ea0e5e54679a6eb4660fdc34"}, {"file": "apps/hr-analytics/styles/_jobs-report.scss", "hash": "2bdde20fffbaace98c1e0120df627f75e1720113"}, {"file": "apps/hr-analytics/styles/_line-chart.scss", "hash": "65de07acfd16454a9ff2b71d530ec3b81f45507b"}, {"file": "apps/hr-analytics/styles/_manage-team.scss", "hash": "e7a81b9b145f7120212a3fccc5b9527990f695fb"}, {"file": "apps/hr-analytics/styles/_members-report.scss", "hash": "ba9f183768ebdccacd73106fe10e676b68fecafc"}, {"file": "apps/hr-analytics/styles/_mixins.scss", "hash": "35dd162aa0853797cc4705fe1628f830da7be339"}, {"file": "apps/hr-analytics/styles/_report.scss", "hash": "1e56cd247f5f66a6302fb8f4b67067d197685b2e"}, {"file": "apps/hr-analytics/styles/answerSheet/_answerSheetScreen.scss", "hash": "373068ca66bff18aa421eacc5cff39a68e7ef33d"}, {"file": "apps/hr-analytics/styles/answerSheet/_question.scss", "hash": "87e3a6d43c48728344bb17b1aa137421ea21ed59"}, {"file": "apps/hr-analytics/styles/datepicker/datepicker.scss", "hash": "9eb8b9803e8a097ba3407721b361a57cd3337e0a"}, {"file": "apps/hr-analytics/styles/datepicker/mixins.scss", "hash": "255978e34dbc387795d3b9cda56a2f629c61390c"}, {"file": "apps/hr-analytics/styles/datepicker/variables.scss", "hash": "48109e34218223e6e229f60f29339fea9a17b78e"}, {"file": "apps/hr-analytics/styles/jobs-table.scss", "hash": "401521527bac5598721ea4d04c752c0cc1964569"}, {"file": "apps/hr-analytics/styles/main.scss", "hash": "692de6fa3b7d39f78779badcc135f520c91bb4aa"}, {"file": "apps/hr-analytics/styles/selectCustomBottomStyle.ts", "hash": "6d560e9f483bdef8ef72076c01ccf5de5f9b99b8"}, {"file": "apps/hr-analytics/styles/selectCustomErrorStyle.ts", "hash": "f013a863158e4a270c7733101787a3690c6224da"}, {"file": "apps/hr-analytics/styles/selectCustomStyle.ts", "hash": "936cdc351672d0d1cdf49883710da4b08d2ee00d"}, {"file": "apps/hr-analytics/styles/selectDisableStyle.ts", "hash": "118b5a21cd0ecc90b4789162d4e857ec01cde76a"}, {"file": "apps/hr-analytics/styles/selectMobileMenuStyle.ts", "hash": "13ff3678aeb4d0ec44aa3867b15621f89fedeb03"}, {"file": "apps/hr-analytics/styles/selectSmallStyle.ts", "hash": "a410c92cc22a2150699270a6d7f93c1a675134ec"}, {"file": "apps/hr-analytics/tsconfig.json", "hash": "3c6dfd9b279695bdcdda59a8db8bde3bd1240e55"}, {"file": "apps/hr-analytics/types/global/global.ts", "hash": "db089a823d54e03ece407e13e6d452ad7e6ebd73"}, {"file": "apps/hr-analytics/types/redux/assessments.ts", "hash": "1682674b79ca2cdd7a391cfb5053a85cca9aa5d9"}, {"file": "apps/hr-analytics/types/redux/job-detail-view.ts", "hash": "81edee1366102b08f9e662ff01b22e7693726f2f"}, {"file": "apps/hr-analytics/types/redux/jobs-offers.ts", "hash": "434187db3a855b3eb97681681da4b8c5d1e24585"}, {"file": "apps/hr-analytics/types/redux/jobs.ts", "hash": "1f82602c62b0bf29694845c6c75cbc20679fb4e3"}, {"file": "apps/hr-analytics/types/redux/members-age-breakdown.ts", "hash": "aeeebc30dfd56366098999f2b2c994240f1330bc"}, {"file": "apps/hr-analytics/types/redux/members.ts", "hash": "05be6b013d7ca9924e5f4fcb9a3444a114f6aa77"}, {"file": "apps/hr-analytics/utils/checkLastAddedItemToSelect.ts", "hash": "60906bdbaf7df4199c3a15b016588e1e300664be"}, {"file": "apps/hr-analytics/utils/constants.ts", "hash": "11f7980c30464632096b33fb253f9877ec5950c0"}, {"file": "apps/hr-analytics/utils/materialTheme.ts", "hash": "91ae54b9141cb26ad4d738cf0238f6ad736018a1"}, {"file": "apps/hr-analytics/utils/monaco/monacoLanguageMiddlewareFunc.ts", "hash": "05f339186d46cdd2581b5009c03dfbe6f7421c94"}, {"file": "apps/hr-analytics/utils/monaco/monacoPostEditor.ts", "hash": "1358b5e3442857360f36b001a59aa6d90c152563", "deps": ["api"]}, {"file": "apps/hr-analytics/utils/monaco/themes.tsx", "hash": "24a59996e91721f4542d93ba35f713c00fbd8ce0"}, {"file": "apps/hr-analytics/utils/sortingFuncForTables.ts", "hash": "5e1b0b21dddab470160821f23323161dae50461f"}, {"file": "apps/hr-analytics/utils/transformDate.ts", "hash": "02e3d12a12f4e2e20255ea8a12bfc3557663b0f1"}, {"file": "apps/hr-analytics/webpack.config.js", "hash": "33baecf55e1cfc943b50d482c7e07fd5c09a74a5"}]}}, "recruitments": {"name": "recruitments", "type": "lib", "data": {"root": "apps/recruitments", "sourceRoot": "apps/recruitments", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/recruitments/.eslintrc", "hash": "c1c13f5b318fdf9c0f72ac6e5ef4a268b286dc69"}, {"file": "apps/recruitments/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/recruitments/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/recruitments/api/fetchDataApi.ts", "hash": "45a0e38758c87eba7cb94f5883d737e2921688c0", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/recruitments/components/AssessmentsDashboard/CodingAssessmentsList.tsx", "hash": "32dcdd5f74a4f34e02d617936ca9482fa273409e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/AssessmentsDashboard/DomainAssessmentsList.tsx", "hash": "13c5208cbcc58a585d224799edeee31e33c3acbf", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/AssessmentsDashboard/ManageAssignmentTable.tsx", "hash": "7671ee5458756f6904df0fe90d5a22baa03d7500", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/AssessmentsDashboard/Popup/wokflowPopup.tsx", "hash": "6e15ab1d65850c282b49bce0c132d88eb292e61a"}, {"file": "apps/recruitments/components/AssessmentsDashboard/ReviewAndScoreList.tsx", "hash": "77349f5b0b2b0aca231f216662594075ae396f91", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/BackgroundScreening/ActionPopupReviewScreening.tsx", "hash": "e14fb39c5f795b3c35e9473ebad58dd00a555d61"}, {"file": "apps/recruitments/components/BackgroundScreening/filters/CandidateFilters.tsx", "hash": "c9e5a01733eff96fc0c403757a899b38a33116d9"}, {"file": "apps/recruitments/components/BackgroundScreening/filters/JobFilters.tsx", "hash": "dace08fc7eb433045958dc2bada748d1da6fb43b", "deps": ["api"]}, {"file": "apps/recruitments/components/BackgroundScreening/index.ts", "hash": "ff5b0e36a53d25c3bef5ce8d4daf866d29d6dd3c"}, {"file": "apps/recruitments/components/CalendarPopup/CalendarPopup.tsx", "hash": "82d4a9a3cd06a36d794e069572130f48742c011c"}, {"file": "apps/recruitments/components/CandidateProfile/CandidateDetailsWrap.tsx", "hash": "f9aea661ffc99cd872f3b1f92b2de08cc4edd484"}, {"file": "apps/recruitments/components/CandidateProfile/DetailsBlock.tsx", "hash": "a95af6cc66afc5f680ac84ed740cd73a2d5954db"}, {"file": "apps/recruitments/components/CandidateProfile/DetailsRow.tsx", "hash": "9df79a4fd9fb666a3ce68de608f892d910fb53b9"}, {"file": "apps/recruitments/components/CandidateScoreboard/ActionPopupScoreboard.tsx", "hash": "5850d3714d84d0a76db8e44162c33b093cd27325", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/CandidateScoreboard/ScheduleMeeting.tsx", "hash": "1e99c0ea85aaf135ad7e2f87d88a66cfb4a46947", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/CandidateScoreboard/UpdatePopupCandidateScoreboard.tsx", "hash": "e971730cae5ccd5e01eb8cb6ba1dd93f71eb8f38", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Dashboard/BackgroundScreeningList.tsx", "hash": "512af8d8a41e80467b1f3a38d9ff8d1d17a4d7ba", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Dashboard/DashboardUserList.tsx", "hash": "ec08914a217f2dee6c2dc119517be23693c89043"}, {"file": "apps/recruitments/components/Dashboard/DrugScreeningList.tsx", "hash": "2d84b8be2fad443c4675362f8120294132318d9d", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Dashboard/InterviewTable.tsx", "hash": "6ba90ecea4b302bfa39f091fa0a2acd2d446d946", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Dashboard/OfferList.tsx", "hash": "0702167b34a188eb441fe1d1f5c3a18418e37cb9", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DetailedViewJob/MatchedProfile/MatchedJobFilter.tsx", "hash": "95a93fa26e74e33100c0a4909e926a58ba9e074d", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DetailedViewJob/MatchedProfile/MatchedProfileTBody.tsx", "hash": "92d37dd41bc4dd205bcef3180dfbc315ab802d5d", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DetailedViewJob/MatchedProfile/MobileTableDetailedView.tsx", "hash": "a6fed9524b85479b4be4123bd1dd9156ebc8c3a8"}, {"file": "apps/recruitments/components/DetailedViewJob/MatchedProfile/StartWorkflowPopup.tsx", "hash": "d8eb2c8c06723f9cea49c26f2ed35aa9f103fef6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DomainAssessment/filter/InputRange.tsx", "hash": "7eafb441d8256ed22093f74c32a44fc299c5f127"}, {"file": "apps/recruitments/components/DomainAssessment/FilterPopupManageDomainAssessment.tsx", "hash": "29bfd8270a2901ab6f3ef14ebbf1ee493ead3031", "deps": ["api"]}, {"file": "apps/recruitments/components/DomainAssessment/ManageDomainAssessmentTBody.tsx", "hash": "c3fc93e576f595433ea6c988ec7f21b0fa64026c", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DomainAssessment/ManageDomainMobileTable.tsx", "hash": "867839cd5586c79bf046374efa8de9cf443f8de1", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DomainAssessment/popup/DeletePopup.tsx", "hash": "006966c545fd4dcc19f50b54d14eaf9bd3faf525", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DomainAssessment/popup/modal.tsx", "hash": "65703c74fd4245c2e2e148290dd63c5429138fc1", "deps": ["api", "live-coding", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DomainAssessment/popup/question.tsx", "hash": "a8ecc802500775b9a6e75e0682d0616636d0fe8d"}, {"file": "apps/recruitments/components/DomainAssessment/popup/RichTextEditor.tsx", "hash": "d7c9c089c225ff4452446f66eabf58ef54653446"}, {"file": "apps/recruitments/components/DomainAssessment/PopupWithInstruction.tsx", "hash": "bde3f5004209fb10b0b319a794f060f23196611e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/DropDown/DropDown.tsx", "hash": "9ec406b47d103b2433ef4960000683454d379c87"}, {"file": "apps/recruitments/components/DrugScreening/filters/CandidateFilters.tsx", "hash": "0e1796b794c115a4993bfc73e2afa942eff929ed"}, {"file": "apps/recruitments/components/DrugScreening/filters/JobsFilter.tsx", "hash": "9afd7cdb0ac47bbfd68b37076cca31d93af264c4", "deps": ["api"]}, {"file": "apps/recruitments/components/DrugScreening/index.ts", "hash": "743ee886b6a361632822299a9c0e9204cbbacd73"}, {"file": "apps/recruitments/components/FileUploadProgress/FileUploadProgressBar.tsx", "hash": "ba97d6deab14bae1847e15644e3816c4f6f55fa3"}, {"file": "apps/recruitments/components/GenerateOfferLetter/AsideModal.tsx", "hash": "c7dbc6c413eb71cecb917b4e77d710fedae940dc", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/GenerateOfferLetter/Progress.tsx", "hash": "8cfff7f698fe42367abf3c6c7fe77638f6e1d06a"}, {"file": "apps/recruitments/components/GenerateOfferLetter/Steps/DetailsStep.tsx", "hash": "18194f45c0ed4357b2a21abf1d0779b9cb1bcc20"}, {"file": "apps/recruitments/components/GenerateOfferLetter/Steps/OfferBodyStep.tsx", "hash": "35b851cbeab75eb5f1237cad0e4a3e86263ad18e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/GenerateOfferLetter/Steps/OfferDetailsStep.tsx", "hash": "9eea10a163e74a569f1561d2194bdd7f95ea2a0f", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/GenerateOfferLetter/TextEditor.tsx", "hash": "bb514b5ab124b601252e6c6447663e23141fabec"}, {"file": "apps/recruitments/components/Global/MultySelectSkills/MultiSelect.tsx", "hash": "f4a27d481f021f843ba8573d244a47685a0c20b8", "deps": ["api"]}, {"file": "apps/recruitments/components/Global/MultySelectSkills/MultiSelectListItem.tsx", "hash": "e257d3846ea599c694ae79d6df009b45d520eda8"}, {"file": "apps/recruitments/components/Global/PageLoader.tsx", "hash": "6d1bcc88d77b280b9e9a52661407406b80cbf717"}, {"file": "apps/recruitments/components/Global/SelectOptions.tsx", "hash": "9b1a8292fd8784be1a37d0047c1f0c43afbb6073"}, {"file": "apps/recruitments/components/Global/SmallLoader.tsx", "hash": "ee47b234ad84875a963db68e0b26f0ef637a7c8b"}, {"file": "apps/recruitments/components/Global/table/FilterWrapper.tsx", "hash": "967bb3a80ad6cd75e7fd49b6b659fac1605863b4"}, {"file": "apps/recruitments/components/Global/table/MemoOrderSVG.tsx", "hash": "10f534a5267771141100610d9ed94fb22ee7da72"}, {"file": "apps/recruitments/components/Global/table/NoResultsSearch.tsx", "hash": "0c72a5954d9c83ea2e77884071ee18905c217b1c"}, {"file": "apps/recruitments/components/Global/table/OrderPopup/DNDOrderPopupItem.tsx", "hash": "6b9eefb38b817b1def9bae88d7f8d8fa62dd24f8"}, {"file": "apps/recruitments/components/Global/table/OrderPopup/DNDOrderPopupWrapper.tsx", "hash": "2f73a78e0106037fb038e74338e8c14c46127475"}, {"file": "apps/recruitments/components/Global/table/OrderPopup/OrderPopupInner.tsx", "hash": "84c4c6b7f0bdc8c8ae360efd3d0cad892e81e94d"}, {"file": "apps/recruitments/components/Global/table/SingleMobileItem.tsx", "hash": "01b9977be776a6baf6fbcae396df482aee9d5be7"}, {"file": "apps/recruitments/components/Global/table/TableCheckbox.tsx", "hash": "8cdd87fa5def0e80381b6a0647ebfcd991ca21df"}, {"file": "apps/recruitments/components/Global/table/TableEmpty.tsx", "hash": "bd4035688d4992bc69b1410b3dcf4470c464ec19"}, {"file": "apps/recruitments/components/Global/table/TopFilterButton.tsx", "hash": "1b3553287fdc4f08d77bc9b831fc720aa93b4c8a"}, {"file": "apps/recruitments/components/HOCs/MTMPopupHoc.tsx", "hash": "e9f8f6cf17f364e653ed4f4abf11c33b49ce8c7c", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/HOCs/OrderPopupHOCs.tsx", "hash": "33bd5d6d32b61b6ce8fbae0ee98d9c43333e0f52"}, {"file": "apps/recruitments/components/HOCs/PositionWorkflowPopupHOC.tsx", "hash": "c3e108f15d364e01973ca5d0a0e7e74789b73337", "deps": ["api"]}, {"file": "apps/recruitments/components/Interviews/filters/CandidatesFilters.tsx", "hash": "00e853b51b9002b0a3c4aaee8a5db51e6ed939b1"}, {"file": "apps/recruitments/components/Interviews/filters/JobsFilters.tsx", "hash": "e5f2762270bdf66f2696d39865807956ffa4df29", "deps": ["api"]}, {"file": "apps/recruitments/components/Interviews/index.ts", "hash": "d9ed9bf58199a00a3377ab37ece4e86424fc208f"}, {"file": "apps/recruitments/components/Interviews/InterviewTypeButton.tsx", "hash": "5f76cd38e3b662e13a052640975339a8364a4063"}, {"file": "apps/recruitments/components/Jobs/JobsFilterItems.tsx", "hash": "32532b49297f140456cce87aa87e95c9685b59c8"}, {"file": "apps/recruitments/components/Jobs/popups/ApproveJobPopup.tsx", "hash": "300380fbb48727fd68cc2b63b393e1e26b064ae2", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Jobs/popups/ChangeWorkflowPopup.tsx", "hash": "9f9d91af5f37341b2623ec3561411402054c55bb", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Jobs/popups/DeletePopupJobs.tsx", "hash": "90bae800f4f1e898c20de51a6b86304e839a6cdb", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Jobs/popups/RejectPopupJobs.tsx", "hash": "1f6f2448815735c208001a4d207890d885989e55", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Jobs/TBody/JobsTBody.tsx", "hash": "2f4b5d75339e8a2384ed8f3578f36f3e8c9c9f94", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Jobs/TBody/MobileTableJobs.tsx", "hash": "1cae78e91af59da86246906bfba4c3a0311c3e95", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Jobs/TBody/ReactSelectWorkflowsJobTable.tsx", "hash": "b3c280cfbd29e5e8880ea184d0a0b34c04f3d712"}, {"file": "apps/recruitments/components/Jobs/TBody/SelectStatus.tsx", "hash": "54139c26adaf982ea4f52b5143e8a6ce0ae3d748", "deps": ["api"]}, {"file": "apps/recruitments/components/JobsOffers/FilterPopup/FilterItemsJobsOffers.tsx", "hash": "8a738267fc20a516e4dfae1b3a720dc50a7dc36e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/JobsOffers/MobileTable/MobileTableOffers.tsx", "hash": "6eb7fd6ecd6a65cf700fad5c7c5c435ee72c7c6c"}, {"file": "apps/recruitments/components/JobsOffers/Table/SelectOfferStatus.tsx", "hash": "72e7b15353612a114bf5348bc22758719de390fe", "deps": ["api"]}, {"file": "apps/recruitments/components/JobsOffers/Table/TBodyInnerJobsOffers.tsx", "hash": "8664c423787307318ac5633228bbcefc33ab1efd"}, {"file": "apps/recruitments/components/Loader/Loader.tsx", "hash": "1d7212fd1bd6fad123f9ee9f639fb6a540b913d5"}, {"file": "apps/recruitments/components/MailPreview/MailPreview.tsx", "hash": "ac3ed75bef310ce979713e57f90d5abeffe97bb4", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageAssignment/AssignmentCandidate/AssignmentCandidateMobileTable.tsx", "hash": "9f8e1dace74e1fe1f5b0e456b92612c4cdafa080"}, {"file": "apps/recruitments/components/ManageAssignment/AssignmentCandidate/AssignmentCandidateTBody.tsx", "hash": "c9c0de0d94dee868e634b2533dd20111d1a5aa32"}, {"file": "apps/recruitments/components/ManageAssignment/AssignmentCandidate/FilterPopupAssignmentCandidate.tsx", "hash": "95a826855ca6be11997678c9a07bb8f3952afdb5", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageAssignment/AssignmentCandidate/PopupWithScheduleOn.tsx", "hash": "8e281597316dadcb57ddef24d1221a4d36efd0c8", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageAssignment/FilterPopupManageDomainAssessment.tsx", "hash": "8b7e5cfc63b255857a2881eac3b85c866fe957bf", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageAssignment/ManageAssignmentMobileTable.tsx", "hash": "034c1206732e6ef4dad392498232bcaacb027dfa", "deps": ["api"]}, {"file": "apps/recruitments/components/ManageAssignment/ManageAssignmentTBody.tsx", "hash": "3b94ac320c3e3ac07792aa18738d5ef9e6975b61", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageAssignment/popup/DeletePopup.tsx", "hash": "80df0749c1e78983c465c2af75b6139677cf0f7a", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageAssignment/PopupWithAssessmentType.tsx", "hash": "e3438979b10f4f333115d7e7d338bd85c01bc360", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageCandidates/CreateCandidatePopup.tsx", "hash": "1c8c913d504b7f51f272a6413b33c1f9e2870a20", "deps": ["api"]}, {"file": "apps/recruitments/components/ManageCandidates/FilterPopupManageCandidate.tsx", "hash": "f6bc5c9cc08b1c55bbbc1a23d9e58541440a7136", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageCandidates/ManageCandidateMobileTable.tsx", "hash": "541e4724b80aa00994818f23f0bf43ee2e186d61", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageCandidates/ManageCandidatesTBody.tsx", "hash": "fad84e8351d4305b82242b6abe4219448d0e98f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageCandidates/ResendPopup.tsx", "hash": "15de372b4914871c2020b3d4e44af845523cb19e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageCandidates/ShowAssignPopup.tsx", "hash": "647441e4eb7f761e8bb742df2ffe1d4973f558b2", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/components/ManageTeamMembers/Popup/ManageTeamPopup.tsx", "hash": "362159bc4de8946ee4d27312d453c31976cfed08", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/components/ManageTeamMembers/Table/DeletePopup/DeletePopupMTM.tsx", "hash": "7f1ed3e3cc5b56eded638784232b54cc4cbfe1d6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageTeamMembers/Table/FilterPopup/FilterItemsMTM.tsx", "hash": "146d34ced2fc5cb742be14e93fa12c8becb1116f", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageTeamMembers/Table/MobileTable/MobileTable.tsx", "hash": "749397e4897f53efbf9157ccac8f96090500631c"}, {"file": "apps/recruitments/components/ManageTeamMembers/Table/MobileTable/MobileTableItem.tsx", "hash": "0fe84e4e196aa4e1cd2405389849da46c465a875", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageTeamMembers/Table/ResendPopup/ResendPopup.tsx", "hash": "f0a2035d42dab22a446baad9162bb6c5a4222736", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ManageTeamMembers/Table/TBody/DropdownRoleMTM.tsx", "hash": "b1d823ea0cec3d091663fc13efbdcc24c82f344b"}, {"file": "apps/recruitments/components/ManageTeamMembers/Table/TBody/TBodyInnerMTM.tsx", "hash": "a52d18b2523d41cd0154842994b555f10e8544f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/add-signature.tsx", "hash": "d2c14c97f53159d13f2c4461e37746f2311314cc"}, {"file": "apps/recruitments/components/Modals/approval.tsx", "hash": "b47fc92ec601027b61f9b62bab97b9dd4e7e3db1", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/close-offer.tsx", "hash": "c11ff025fb150c64fb63a2c12ce9555b81242f2a", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/delete-offer.tsx", "hash": "16b382b95af6eed21fc844ccd9c03a827448a466", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/delete-template.tsx", "hash": "981d29e635b0196f6858d183cf15ddd356ea06aa", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/draft.tsx", "hash": "68fb11ff1f4d682c8ba81902296f71e6153aa19f"}, {"file": "apps/recruitments/components/Modals/index.ts", "hash": "61f9ef237e25e6672bdb26f6213754fcc932d62d"}, {"file": "apps/recruitments/components/Modals/offer-approved.tsx", "hash": "bc3376f55b2cb4603152236b5c7101aa66f38481", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/offer-rejected.tsx", "hash": "3d7abba840f9d91e3062d5ce0a0f5a96f4554944", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/save-template.tsx", "hash": "cf096aff1a6a5ee1abd822d2f2ddb40f2083b472", "deps": ["api"]}, {"file": "apps/recruitments/components/Modals/saveOffer.tsx", "hash": "50780cd0c2c50819e54023f49d6f35eaf43084bf"}, {"file": "apps/recruitments/components/Modals/select-screening.tsx", "hash": "ef23b8e8733944416b86b11a65b9e632ae50f599", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Modals/success.tsx", "hash": "52110a5b8da2d38222ceb3747979d54e5a1318c4"}, {"file": "apps/recruitments/components/OfferPreview/OfferAccordion.tsx", "hash": "b24ad34278772277ac718ac4f892b59d86df6b22"}, {"file": "apps/recruitments/components/OfferPreview/OfferPreview.tsx", "hash": "b0423a7a97086cb3c9fc4168674339c77b2bd999", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/OfferPreview/OfferPreviewHeader.tsx", "hash": "8913a54a21d197655b02b9f7bc2b0400a320e478"}, {"file": "apps/recruitments/components/Popups/editor-modals.tsx", "hash": "083da209b910384e196009c2b63bc77d86cae7ee", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Popups/Modal.tsx", "hash": "9641c2934ee2397ec9e7b7888053591029483148"}, {"file": "apps/recruitments/components/PositionWorkflow/chatbot/ActionProvider.tsx", "hash": "3711ec002a70043cb9b6f1a5f17685fdf120276d", "deps": ["api"]}, {"file": "apps/recruitments/components/PositionWorkflow/chatbot/config.ts", "hash": "c370b5cc19256b28b4ffe985dbee385dd2bd84c3"}, {"file": "apps/recruitments/components/PositionWorkflow/chatbot/CutomComponents/PwBotAvatar.tsx", "hash": "751a5f81a50db37c31561d7f4da6264aa943c743"}, {"file": "apps/recruitments/components/PositionWorkflow/chatbot/MessageParser.tsx", "hash": "325ecad6c9a4fd12d37c24f7df050d2d51c20d64"}, {"file": "apps/recruitments/components/PositionWorkflow/chatbot/WorkflowCreationChatbot.tsx", "hash": "048929d0c737f5368cdeaa920d30bec3fece3ee7"}, {"file": "apps/recruitments/components/PositionWorkflow/Popup/DNDItem.tsx", "hash": "878034d2a2965c79b4a60869ae833a838af44ede"}, {"file": "apps/recruitments/components/PositionWorkflow/Popup/DNDWorkflowsWrap.tsx", "hash": "362c208c5a9d572baba1e0a3ec4c706d59f4bbbc"}, {"file": "apps/recruitments/components/PositionWorkflow/Popup/FunctionalAssessment.tsx", "hash": "a3455a7f3b75f62f3dd11afcbae0ae755ab7b508", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/PositionWorkflow/Popup/PWPopup.tsx", "hash": "7ac1ec10967dad4e8f2feeb246368e5e10961958", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/PositionWorkflow/Popup/TechnicalAssesment.tsx", "hash": "a636789939a94a8a688ca50fd4f6ff3ef1e18198", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/PositionWorkflow/SingleSelectOptionWorkflows.tsx", "hash": "f8cefc0deab5bc0bf0af5b0c59b842657ca59c50", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/PositionWorkflow/table/DeletePopup.tsx", "hash": "9f9997883afaae925798b2357c41590a6fdb4def", "deps": ["api"]}, {"file": "apps/recruitments/components/PositionWorkflow/table/MobileTableWorkflows.tsx", "hash": "cabbb9ff26da75e95e078c14f0ba2dc831c340f0", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/PositionWorkflow/table/PopupAssignWorkflow.tsx", "hash": "25a00e02aafc0b83e48e3497a275bf357411eea0", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/PositionWorkflow/table/PopupWithList.tsx", "hash": "85e77356745606e14146942a38ac95501a191bca", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/PositionWorkflow/table/TBodyWorkflow.tsx", "hash": "353a3e468a87c12bbb540f9c0741c4bbf07da9a6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/RecruitmentDashboard/CompanyLinks.tsx", "hash": "96a33f62aac4af27d4ceb580e991d119adbb49da", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/RecruitmentDashboard/JobsTable.tsx", "hash": "ee811466e106658e25cdbc47d5b666ef246c88d1", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/RecruitmentDashboard/PositionWorkflowList.tsx", "hash": "ed3ee8dd3e20e6e03fa4140bf9733138a60a7dc3", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/RecruitmentDashboard/TeamMembers.tsx", "hash": "6a38e3032fb2e145f8d1f522fc5e3a2c89ae31bf", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/CandidateDomainAssessment/filter/InputRange.tsx", "hash": "349abc0f0e6d0994e5b232088fb7bb2d434f8156"}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/CandidateDomainAssessment/FilterPopupManageDomainAssessment.tsx", "hash": "5e4191f815af02b33309a5c78f2a0b3a68f8012e", "deps": ["api"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/CandidateDomainAssessment/ManageCandidateDomainMobileTBody.tsx", "hash": "6e28e1b385ae61db80cca3857b3afaf077def5c2", "deps": ["api", "live-coding"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/CandidateDomainAssessment/ManageCandidateDomainTBody.tsx", "hash": "bcb0cbe47761c161383641e0bdac6d6bc8c10587", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/CandidateDomainAssessment/QuestionPreview/feedback.tsx", "hash": "ab020a27e8cfb5a33f8204d967c8c0742ce991ea", "deps": ["api"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/CandidateDomainAssessment/QuestionPreview/question.tsx", "hash": "7924e08a6be16e171994fd2976ecd342e6804f9b", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/filter/FilterPopupDomainAssessmentReview.tsx", "hash": "2e33702aa3d225e6ee0fe290acf98f697cb31fac", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/ManageDomainReviewAndScoreTBody.tsx", "hash": "e0fa407ef89bb7da47d7ab301f7d9ea072b975fb", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/reviewAndScore/DomainAssessment/ManageReviewMobileTBody.tsx", "hash": "47853ee25c758031f9ca23c4a664fca9bfe94562", "deps": ["api"]}, {"file": "apps/recruitments/components/reviewAndScore/table/DeletePopup.tsx", "hash": "f57020b56721b5cf56c93b6b781429c6f33df1dc", "deps": ["api"]}, {"file": "apps/recruitments/components/reviewAndScore/table/PopupWithList.tsx", "hash": "33760ab9d92aec3af907d7c8276b38f0afe147ac"}, {"file": "apps/recruitments/components/reviewAndScore/table/TBodyWorkflow.tsx", "hash": "30559076469d078fea2df4090597be665a3a2038"}, {"file": "apps/recruitments/components/reviewAndScore/table/WorkflowMobileTBody.tsx", "hash": "0ba394ae6077c1ef499397f55ed79cb9500fc0ba"}, {"file": "apps/recruitments/components/reviewAndScore/TakeHomeSubmissionDetail.tsx", "hash": "e04c6fff26053be07fda290a8aeab70ad6566f36", "deps": ["api"]}, {"file": "apps/recruitments/components/Scoreboard/Filter.tsx", "hash": "9016d4c27883371eed62efc0dab27bd58277b3f1", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/Scoreboard/Popup/WorkflowPopup.tsx", "hash": "4bbf51ae5e7ac78977c03653204f18e8fa362ad2"}, {"file": "apps/recruitments/components/ScreeningAndHiring/CalendarDayView/CalendarView.tsx", "hash": "a477a1777d36909085f94c2f8e636ef53e041a24", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/ScreeningAndHiring/CalendarDayView/EventView.tsx", "hash": "12b2fa07a48c07a099d4371d472b84b2280d829e", "deps": ["api"]}, {"file": "apps/recruitments/components/SendMail/SendMail.tsx", "hash": "ece4f48e166f9ae247e3663ff0bb6046183072ba", "deps": ["api"]}, {"file": "apps/recruitments/components/Table/CandidateTableHeader.tsx", "hash": "ced86d2b1d676ce6ec5ae55ebd90a63e30a4eb02"}, {"file": "apps/recruitments/components/Table/MailBoxTable.tsx", "hash": "1bb2ca5543e573cdafc84790d366d2870290b00c"}, {"file": "apps/recruitments/components/Table/S3StorageTable.tsx", "hash": "09c83cadeded1e8b30a67e2f14aab8caadff3424"}, {"file": "apps/recruitments/components/UsersOffers/FilterPopup/FilterItemsUsersOffers.tsx", "hash": "ec6f294194b2c7c057c6a0484a0ab62f8ded34ce", "deps": ["api"]}, {"file": "apps/recruitments/components/UsersOffers/MobileTable/MobileTableUsersOffers.tsx", "hash": "e7844b0388c7085a3bb5a7232937779f106de851", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/components/UsersOffers/Table/TBodyInnerUsersOffers.tsx", "hash": "773d15d1a3b24d4550db7f6c1a0be19fea03043d", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/enums/assignment-candidate/assignmentCandidate.ts", "hash": "096d927394966dc914358dc7a84d4baa3ef7d501"}, {"file": "apps/recruitments/enums/background-screening/candidates.ts", "hash": "727488f88800e1673e339dda55ca8c56e04c049d"}, {"file": "apps/recruitments/enums/background-screening/jobs.ts", "hash": "a174324426a8a5c3c45c6bebeb6a73abb4187274"}, {"file": "apps/recruitments/enums/candidate-domain-assessment/candidate-domain-assessment.ts", "hash": "bc36c5decdad636560ede1a7bb2456b9ab68fa74"}, {"file": "apps/recruitments/enums/candidate-scoreboard/round-types.ts", "hash": "00fef749dec0d9099689b5a6255f4bb4d0a70fcc"}, {"file": "apps/recruitments/enums/coding-assessments/coding-assessments.ts", "hash": "4676065a94aefdf033f6c0e12354774f89fde0b8"}, {"file": "apps/recruitments/enums/detail-view-job/detail-view-job-matched.ts", "hash": "206c325596a5ac02fdb2e1056b7f95cdf9fe939b"}, {"file": "apps/recruitments/enums/domain-assessment/domain-assessment.ts", "hash": "1359e013fb090ddb9a558d4bd247eb208684124f"}, {"file": "apps/recruitments/enums/drug-screening/candidates.ts", "hash": "b6ef88b87bacfa8c5136199cbfc90316cca97666"}, {"file": "apps/recruitments/enums/drug-screening/jobs.ts", "hash": "70e54a62eb1b1ae90824664096cc7c177023430a"}, {"file": "apps/recruitments/enums/generate-offer/generate-offer.ts", "hash": "6dc5310eaf4dd2775e6072743a66305befafeb87"}, {"file": "apps/recruitments/enums/generete-offer/generate-offer.ts", "hash": "6dc5310eaf4dd2775e6072743a66305befafeb87"}, {"file": "apps/recruitments/enums/index.ts", "hash": "8dbae77ecd85fbc0370cd8e6546aa921a2bfd550"}, {"file": "apps/recruitments/enums/interviews/candidates.ts", "hash": "de83300647dbd47228cb09abfc75f71e5f36e67c"}, {"file": "apps/recruitments/enums/interviews/common.ts", "hash": "4c8b6b591fc0a38e1df1cf4ed549c6745897c55e"}, {"file": "apps/recruitments/enums/interviews/jobs.ts", "hash": "24b58f586d4e9cab1c65d9e18b71a1855b8b04dd"}, {"file": "apps/recruitments/enums/jobs/jobsEnums.ts", "hash": "35a1f1e5f8ee66d7acf2de48c02538c8f34ef3a9"}, {"file": "apps/recruitments/enums/jobsOffers/JobsOffersEnums.ts", "hash": "0b2a8f276173024cb5f1e54148e597112a3a7c28"}, {"file": "apps/recruitments/enums/live-coding/liveCodingEnums.ts", "hash": "c9753ef65eb33992acea3dadacd212a0a3fbb8a4"}, {"file": "apps/recruitments/enums/manage-assignment/manageAssignment.ts", "hash": "466ffe3e55b34e435754934b82e84c33b69bb3c9"}, {"file": "apps/recruitments/enums/manage-candidate/ManageCandidateEnums.ts", "hash": "777dafd631e28f11179ec13d1dbfeac187543f54"}, {"file": "apps/recruitments/enums/manage-team-members/ManageTeamMembersEnums.ts", "hash": "9f1850765aa44978fdf2bb72773926aa96e7f8d8"}, {"file": "apps/recruitments/enums/reviewAndScore/domainReviewAndScore.ts", "hash": "09fe5dcf23c002ea8b7fcb3d652e0d7f189e7f90"}, {"file": "apps/recruitments/enums/reviewAndScore/ReviewAndScoreEnums.ts", "hash": "3e1e29afcfc5483161dc15696830bc173075df7a"}, {"file": "apps/recruitments/enums/screening-type/screeningType.ts", "hash": "3bed8076130044fe6afc2b2e8bc8488e1972db45"}, {"file": "apps/recruitments/enums/usersOffers/UsersOffersEnums.ts", "hash": "d4600848011352ef627519fb1b66d3b40b89e629"}, {"file": "apps/recruitments/enums/usrsOffers/UsersOffersEnums.ts", "hash": "5ebd1b7de8ef66f3d10ec4ebfe9987ae1ebeab9d"}, {"file": "apps/recruitments/enums/workflow/WorkFlowEnums.ts", "hash": "717e6d6c169efaf40447cc863af471e2e43bfdf8"}, {"file": "apps/recruitments/hook/emitter.ts", "hash": "68b633f5530dbffdacf4d58ff5ab1acc33a901db"}, {"file": "apps/recruitments/hook/http.ts", "hash": "0a6d48531355b4497250f3cb43f097b1e1413dd4", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/hook/index.ts", "hash": "1c285f6e39be41dbaa4019cb46f7ea048c1a4c2a"}, {"file": "apps/recruitments/hook/numberWithCommas.tsx", "hash": "475675b50f6171c739a5c71cd55c9e5bea174230"}, {"file": "apps/recruitments/hook/useClickOutside.ts", "hash": "7fe6045f7460948ae1e0816013bc5f1e8c449321"}, {"file": "apps/recruitments/hook/useDimensions.ts", "hash": "defa2de5976cdf59ed10d5a299707e764caf1967"}, {"file": "apps/recruitments/hook/useScrollLocations.ts", "hash": "52317d2af14bfb25d4d3a463412414c13bfe4722", "deps": ["api"]}, {"file": "apps/recruitments/hook/useSetting.ts", "hash": "c5f613d19c1e292765c082fa24f9ee924eb38c80"}, {"file": "apps/recruitments/hook/useSnackbar.tsx", "hash": "2201eaa5f052df7f2b723c0a68fbaf34971c8aee"}, {"file": "apps/recruitments/hook/useTableClickAndDragScroll.tsx", "hash": "b186aee26af74a8a4ebb4d606e8b763e1ae9ea17"}, {"file": "apps/recruitments/hook/validateEmail.ts", "hash": "be3b985e733a1523650efa33ff688f354e8fa53e"}, {"file": "apps/recruitments/hook/validatePhoneNumber.ts", "hash": "e7e1e97fa068915a022144259f4e76abe6bfe570"}, {"file": "apps/recruitments/image/document.svg", "hash": "1d4ad708980bc967b9888866781eae5cf258ce8e"}, {"file": "apps/recruitments/image/greeting_img.svg", "hash": "bd6a1c0c0992027d46dc7ae6ec5603bbd40a5475"}, {"file": "apps/recruitments/image/Group_455.svg", "hash": "ed8deb67686ca7286728584d161aba4fd3a7dee5"}, {"file": "apps/recruitments/image/icon/approve_job_ic.svg", "hash": "88019e070c7a267a3e16aa7b6eb166179269804c"}, {"file": "apps/recruitments/image/icon/avatar.svg", "hash": "a6dfdace3013cadec974a9b5e89b193268aa4c0a"}, {"file": "apps/recruitments/image/icon/book-open.svg", "hash": "ad0fbbff04aff34ff632e8d8b03ed2071936dcaf"}, {"file": "apps/recruitments/image/icon/calendar_ic.svg", "hash": "05f6771e1475c792445c092090645cc6b0dfc105"}, {"file": "apps/recruitments/image/icon/chat_candidate_profile.svg", "hash": "92c478f153212d49e9521ba51eacd99dab7c4f22"}, {"file": "apps/recruitments/image/icon/chatbot.svg", "hash": "02b8fcda0850be2899cd955216390508b69a959c"}, {"file": "apps/recruitments/image/icon/clock_ic.svg", "hash": "2d08f33c20f0cff1f940bd2d11790c77dadeabf6"}, {"file": "apps/recruitments/image/icon/Close.svg", "hash": "52fdcbd8986403fb52049057bf07378467013b06"}, {"file": "apps/recruitments/image/icon/create_order_ic.svg", "hash": "ef7d80782a71d5607111cc33f488d0013a8e3d68"}, {"file": "apps/recruitments/image/icon/cross.svg", "hash": "746d1ad7cba2c65ad9d9da725304e2bb67dc3128"}, {"file": "apps/recruitments/image/icon/cw_ic.svg", "hash": "8319ddcd7ddfd9b8deb8fc32c7dc54f6224373de"}, {"file": "apps/recruitments/image/icon/delete_ic.svg", "hash": "9ca29516d488f6e4ebdb1a717a11938013003421"}, {"file": "apps/recruitments/image/icon/delete_image_popup_ic.svg", "hash": "0b7567b7592f9d3280b705c6a2b9ccb07fdc9705"}, {"file": "apps/recruitments/image/icon/done__inactive_ic.svg", "hash": "6b4094faa42c733d61c02a58cdabbc67331041e6"}, {"file": "apps/recruitments/image/icon/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/recruitments/image/icon/download_ic.svg", "hash": "9af7231119296b2e183f193514a821a1c206c88e"}, {"file": "apps/recruitments/image/icon/dropdown_down.svg", "hash": "5c9c7bff3033da6a05283ceaaef4becbb44390e4"}, {"file": "apps/recruitments/image/icon/dropdown_up.svg", "hash": "1d811b887cce2adb064e1178a252637ecfa186b7"}, {"file": "apps/recruitments/image/icon/edit_ic.svg", "hash": "5f3b472b990252dbffb690826248bda2045ef414"}, {"file": "apps/recruitments/image/icon/email_candidate_profile_ic.svg", "hash": "24ac72fa3baee43001e1ba9eb755c3c475713eec"}, {"file": "apps/recruitments/image/icon/email_candidate.svg", "hash": "e69f06c04dfbe897e1f73814ef85fa40aa266512"}, {"file": "apps/recruitments/image/icon/email-solid_green_ic.svg", "hash": "72cb678d0bd64888988bc13e98d2f4bb8257b043"}, {"file": "apps/recruitments/image/icon/eye-on.svg", "hash": "48ee0ccc0d2630ad92ba15e71d8b57ce95a45dbb"}, {"file": "apps/recruitments/image/icon/file_ic.svg", "hash": "c71b9163a0fc34f018cca5f4685cb270e298d47a"}, {"file": "apps/recruitments/image/icon/form-arrow_ic.svg", "hash": "7c79ca85c67951bbd9379a2de97ccf3ed6a8c2dc"}, {"file": "apps/recruitments/image/icon/info_ic.svg", "hash": "2a47846f2e17314c5991014317880ccb2ded00db"}, {"file": "apps/recruitments/image/icon/magnet.svg", "hash": "97bfebe528f3f76fbda4356b16b556d9d0edd48e"}, {"file": "apps/recruitments/image/icon/match.svg", "hash": "e532370aa48a696cb1d227bead65169073577acb"}, {"file": "apps/recruitments/image/icon/message.svg", "hash": "5d455de072382f402f3413eafaf57b3049b610f4"}, {"file": "apps/recruitments/image/icon/phone_candidate_profile_ic.svg", "hash": "7f3d896cc2512804921f33aaa64954472e730753"}, {"file": "apps/recruitments/image/icon/phone-solid_green_ic.svg", "hash": "690de097c4644efc1d3bf6e2cba11603d3fb7ba6"}, {"file": "apps/recruitments/image/icon/plus_ic.svg", "hash": "afcc88ecdd5d841dfccda9775bfa059f4999a1f4"}, {"file": "apps/recruitments/image/icon/red_edit_ic.svg", "hash": "3176d977556515fb00054fd18184a9be44de6769"}, {"file": "apps/recruitments/image/icon/reject_job_ic.svg", "hash": "8a7dee66d7b908222401c701770e83ea95802ac2"}, {"file": "apps/recruitments/image/icon/resent invite_ic.svg", "hash": "1728674f1046142420dfebab1641f7239e7ab031"}, {"file": "apps/recruitments/image/icon/reset_ic.svg", "hash": "f7384a5d57ac044338e4ec8b7ed088ec38f2c952"}, {"file": "apps/recruitments/image/icon/save_ic.svg", "hash": "ea61d1da58599e4dd91a8991d378ac396226cb58"}, {"file": "apps/recruitments/image/icon/send_ic.svg", "hash": "53ff60f040cee3d2d0238bee6d8ed6c63fd75be4"}, {"file": "apps/recruitments/image/icon/star_ic.svg", "hash": "267a8a19a44ea59d9fb48f3edddb29f0e6f66a03"}, {"file": "apps/recruitments/image/icon/success_image.svg", "hash": "524f030321ec0c5779e19acf20e0d13f05263535"}, {"file": "apps/recruitments/image/icon/table/empty-table.svg", "hash": "67f8903755aa519965e3fa11bcdff2d832453852"}, {"file": "apps/recruitments/image/icon/table/no-results.svg", "hash": "f3f308f1b83e66d88b296a62eee0aec610ac4422"}, {"file": "apps/recruitments/image/icon/tick.svg", "hash": "4516088f928ef310795170a86a2a714c08952b9a"}, {"file": "apps/recruitments/image/icon/Vector.png", "hash": "b5bf06ba02c98aa95836a5d182c4156a24213f7d"}, {"file": "apps/recruitments/image/icon/video_candidate_profile.svg", "hash": "3395d3f41db06603f8e7b2fdfba5e0784f253f55"}, {"file": "apps/recruitments/image/icon/workflow_add.svg", "hash": "4f18d367474d4d8127b5e154c3a5360cf8d0abf4"}, {"file": "apps/recruitments/image/no-data.svg", "hash": "cdab8a3384a87ab386f8e735ae1852a3890c67e4"}, {"file": "apps/recruitments/image/page-loader.svg", "hash": "85aa65aac1ebf5818f16f172ec1edb869eb560ac"}, {"file": "apps/recruitments/image/temp-user.png", "hash": "16418549d13ec89d7053b61c10fb42fe1de04e2d"}, {"file": "apps/recruitments/image/Vector.svg", "hash": "68834b9d50062fbf408649f8d5c569974a574218"}, {"file": "apps/recruitments/image/view-svgrepo-com.svg", "hash": "4c016b486d5bc258c5bf93ac6d1fa83c747cb267"}, {"file": "apps/recruitments/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/recruitments/package.json", "hash": "3d6bdf7799bc0843117be990c616cd5d34522572", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/recruitments/screen/AnswerSheetScreen.tsx", "hash": "0998c7467b1e78f7571c563579fe00f864e1a586", "deps": ["api", "live-coding", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/AssessmentsDashboard.tsx", "hash": "18cf076deb9514795b2b2fa63ee8d1407dcca23f", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/AssignmentCandidateScreen.tsx", "hash": "74fd907cab14288bb749dd1554795e82549e5a15", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/BackgroundScreeningCandidates.tsx", "hash": "7db5dbd1d4d72e1a66338e18655437ff9e87601c", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/BackgroundScreeningJobs.tsx", "hash": "64cadedbfb2eff56cd7b8555946e21d1edd7c238", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/CalendarScreen.tsx", "hash": "252a04dd5e6ee034ed391a50484924e2469487a7"}, {"file": "apps/recruitments/screen/CandidateDomainScreen.tsx", "hash": "6b7c88247a1887043a0843ed4242279813607287", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/CandidateScoreboardScreen.tsx", "hash": "b20b4c95968d611b34810a504a642c5acbbe6c48", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/DetailViewJobTableMatched.tsx", "hash": "c5f44f1f2607e9a609b975587b162e2c66bcb1c6", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/DomainAssessmentReviewAndScoreScreen.tsx", "hash": "33ace63e1114b54e52d5a3276a8e94becf960559", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/DomainAssessmentScreen.tsx", "hash": "2971bd102c930541ef4d8633d216f9fc5e627d38", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/DrugScreeningCandidates.tsx", "hash": "a8045d8dfa012dfad7907965142761993e158e0d", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/DrugScreeningJobs.tsx", "hash": "c026f5cfc4bedb7dac16e440187b4c0f5b4d8f73", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/GenerateOfferLetterScreen.tsx", "hash": "ef3a12466d59d10787ebfbaf715cf50851f0a9e6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/HellosignScreen.tsx", "hash": "e47a76ce7cace2318ebb3ffc6de02829f40b6103"}, {"file": "apps/recruitments/screen/index.ts", "hash": "2c0ce46152365adf626ecd8540a75023e1b0f4f6"}, {"file": "apps/recruitments/screen/InterviewsCandidates.tsx", "hash": "06f301202d92499af59d244db6de8883c41b7de1", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/InterviewsJob.tsx", "hash": "0f49be2577927289252788f06281be3d49d59bd3", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/JobsOffersScreen.tsx", "hash": "71c0333bd624c43f528fc56dbe4cd7d76258265a", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/JobsScreen.tsx", "hash": "1f8e162c9e30bd8be86d40a0a47a9a4d4dce8b94", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/MailBoxScreen.tsx", "hash": "d212c808efd528e74412ebb195f2102005df74f8", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/ManageAssignmentScreen.tsx", "hash": "206a458a3010b068b1c5faa5cd2f0a635a42c343", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/ManageCandidateProfileScreen.tsx", "hash": "281e5ffe32451689f3be8100948274c6ffbfa15b", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/ManageCandidatesScreen.tsx", "hash": "50b7d61c0316840e868bcf0547d49aa64daeeded", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/ManageTeamMembers.tsx", "hash": "8b7aeb088da50018f5e9eaefb442541066ecfeb7", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/OfferPreviewScreen.tsx", "hash": "1cf9528b16a25d0fd36240058cbabca0a3378258", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/OfferViewScreen.tsx", "hash": "4458022c3eddfca2dcbc61c05e003c9147f7889a", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/PositionWorkflowScreen.tsx", "hash": "2e9c04996bc347e9c333a4d89907186f0eaec1ef", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/RecruitmentDashboard.tsx", "hash": "aa739da65749bfe2cf8a21d86fcca944556c5c49", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/Review.tsx", "hash": "27996ee71b82701d24e8c407d12fcc0c716ef446", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/ReviewAndScoreScreen.tsx", "hash": "e033c86b6db46da77510e5e3a6ff1ac1246f1a0e", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/screen/ScoreboardScreen.tsx", "hash": "f26105c5a787022998c9a742e072edf43b1d36d5", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/ScreeningHiringCandidateDashboard.tsx", "hash": "1466885c084ddcc0db5232f5bf9a0109a60e5a71", "deps": ["api"]}, {"file": "apps/recruitments/screen/ScreeningHiringDashboard.tsx", "hash": "3d44678829498b339a8a7f660755ced168b153a2", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/UnauthorizedModal.tsx", "hash": "e46e68108c37279691c55a3a5622125d5958e5cb"}, {"file": "apps/recruitments/screen/UserInformationScreen.tsx", "hash": "f386d0d6e5ea9f6f6b7010ed660d1b5b70eca677", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/UsersOffersScreen.tsx", "hash": "99631a5007a113a330d28d5172445b82b16cd01e", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/screen/VideoMeetLoader.tsx", "hash": "0be45ed82019bfc95c2d3644a3015b441eca9f9d"}, {"file": "apps/recruitments/screen/ViewScreeningReport.tsx", "hash": "45972f387e3333e7c40b1297eaa90de039663b69", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/recruitments/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/recruitments/src/root.component.test.tsx", "hash": "ef1e9ce9aa95a15bdc7d000d4db1acccba108fdd"}, {"file": "apps/recruitments/src/root.component.tsx", "hash": "5708c1fd2b654637d9786211f1f1acc620bb717a", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/src/urecruits-recruitments.tsx", "hash": "23e936c0a9916f30e17053d6e8b5a5a7f0c1d4f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/recruitments/store/index.ts", "hash": "2ff8e66969bb8af862155b7510baa019cf3c909f"}, {"file": "apps/recruitments/store/reducers/AnswerSheetReducer.ts", "hash": "b8d3fd17a82f61c221238b0bacd8e248b70307ab"}, {"file": "apps/recruitments/store/reducers/assignmentCandidateReducer.ts", "hash": "89cd54eb4b71ed44be698f0b0fa2e42a121d0a6a"}, {"file": "apps/recruitments/store/reducers/backgroundCandidatesReducer.ts", "hash": "718b2b07ef86da57c374d87d1827ea13480364de"}, {"file": "apps/recruitments/store/reducers/backgroundJobsReducer.ts", "hash": "a0305df0b84370591f45b110d1ca86ca6342f1d1"}, {"file": "apps/recruitments/store/reducers/candidateDomainAssessmentReducer.ts", "hash": "b2137a119a0d6d2b3a845098bcded232a84f639e"}, {"file": "apps/recruitments/store/reducers/candidateProfileScreen.ts", "hash": "5b62b225d33ae681b74be0093f21408535767a45"}, {"file": "apps/recruitments/store/reducers/candidateScoreboardScreenReducer.ts", "hash": "2a25af4d189376dedf2a445d2e5fbc7dea4fdcfa"}, {"file": "apps/recruitments/store/reducers/domainAssessmentReducer.ts", "hash": "4374a2cc1c098c01e2f155eb954d41fddb6deb39"}, {"file": "apps/recruitments/store/reducers/domainReviewAndscoreReducer.ts", "hash": "ca1a7353682d771890d51e1955f93859b22c753c"}, {"file": "apps/recruitments/store/reducers/drugCandidatesReducer.ts", "hash": "1088814146e04f7543cafcec00081a673c666bee"}, {"file": "apps/recruitments/store/reducers/drugJobsReducer.ts", "hash": "d7e5a611b6eab2bbda42e97fc1d34254812d59e9"}, {"file": "apps/recruitments/store/reducers/generateOfferLetterScreen.ts", "hash": "398485edc6650460d846e582f9b8aac019a9f7c9"}, {"file": "apps/recruitments/store/reducers/interviewsCandidatesReducer.ts", "hash": "113c191a5aa29344ab44f165bf8c2830293874d8"}, {"file": "apps/recruitments/store/reducers/interviewsJobsReducer.ts", "hash": "006bd958667f24d8dc6a7fe368d071513b8bb7f7"}, {"file": "apps/recruitments/store/reducers/jobDetailViewReducerMatched.ts", "hash": "2ec976383ab04ecdff8af51ec7a9224613377147"}, {"file": "apps/recruitments/store/reducers/jobsOffersReducer.ts", "hash": "998e4097e88207053698a824dac380ea525b55f9"}, {"file": "apps/recruitments/store/reducers/jobsTableReducer.ts", "hash": "271b730abec942c59effb8a6b9b592caeb28dfd9"}, {"file": "apps/recruitments/store/reducers/liveCodingReducer.ts", "hash": "2d68fbf27814f7929d1cfabb464714994a1f5e87"}, {"file": "apps/recruitments/store/reducers/manageAssignmentReducer.ts", "hash": "054c2e93eba308a617d544d830ed2b2c695d0af5"}, {"file": "apps/recruitments/store/reducers/manageCandidate.ts", "hash": "b48f0e290edc8cca010513d3ff36394af7ac4872"}, {"file": "apps/recruitments/store/reducers/manageTeamMembersReducer.ts", "hash": "55e73cd07407611f58a45544d058068c3435a177"}, {"file": "apps/recruitments/store/reducers/offerLetterReducer.ts", "hash": "d5315199917876bc78a430fba28dadf3f7fd7b52"}, {"file": "apps/recruitments/store/reducers/positionWorkflowPopup.ts", "hash": "40bfdbb57d9c5ddf9db6d00613e4966fd6f926f2"}, {"file": "apps/recruitments/store/reducers/positionWorkFlowReducer.ts", "hash": "abc6adc42adf58c01c62a9bc74b30457fcb4f3ab"}, {"file": "apps/recruitments/store/reducers/reviewAndScoreReducer.ts", "hash": "cde93299afabca5bdfa452809ad4d6b5629d6f4e"}, {"file": "apps/recruitments/store/reducers/scoreboardReducer.ts", "hash": "66d1549ea4e10b79ac289bfdcd6d67a159d98c72"}, {"file": "apps/recruitments/store/reducers/usersOffersReducer.ts", "hash": "7f0ab6aebd41599fe346b1d1011f1f7684851951"}, {"file": "apps/recruitments/styles/_assessment.scss", "hash": "28dff2f112ad1bc031bb82fcc0447812046d8826"}, {"file": "apps/recruitments/styles/_background-screening.scss", "hash": "138dd9176c326b53e5c36025906d91f2317bb323"}, {"file": "apps/recruitments/styles/_calendarDayView.scss", "hash": "3f11ba2b265b46fefaffe48fe283b8660c7c469f"}, {"file": "apps/recruitments/styles/_candidate-profile.scss", "hash": "3e0d4b5b9f5a0a8a833f8695b40ba0039163add4"}, {"file": "apps/recruitments/styles/_config.scss", "hash": "ca498c144478ff5719d67cb11811ecbcb3d95102"}, {"file": "apps/recruitments/styles/_create_template.scss", "hash": "62d783459cf102fd265ee94da472f3e5fcf3a40e"}, {"file": "apps/recruitments/styles/_custom-phone-input.scss", "hash": "38839746226fa485b7b22e05f58b1585b442d802"}, {"file": "apps/recruitments/styles/_dashboard.scss", "hash": "2c6a1e02b41fc12dfc9b8a6ccfc8ec64fe9e7c35"}, {"file": "apps/recruitments/styles/_dialog.scss", "hash": "2cceb4ac19dd2fa451c3125854e9f692398cb411"}, {"file": "apps/recruitments/styles/_domain.scss", "hash": "5f2a8f228f8d05f0bfdbbfefae46d0308de71ab6"}, {"file": "apps/recruitments/styles/_drug-screening.scss", "hash": "256028858792f9114e411c0bfa6123769ba4ece0"}, {"file": "apps/recruitments/styles/_feedback_popup.scss", "hash": "475e84e2d9cb267325942759884a6d40c7ac33a1"}, {"file": "apps/recruitments/styles/_generate-offer-letter.scss", "hash": "0cb4a5580b53cf8e4bf0180869c71f9e66ee6df4"}, {"file": "apps/recruitments/styles/_inputRange.scss", "hash": "4bed5394d55418c220cf71338eb2b4d984c990e2"}, {"file": "apps/recruitments/styles/_instruction_popup.scss", "hash": "e247d3d0aafb3cc1a5c0e570dc0f2f6ab2bebdce"}, {"file": "apps/recruitments/styles/_interviews.scss", "hash": "4a2bdfe0e99773ed8209d5c75d2090be73a8d5d0"}, {"file": "apps/recruitments/styles/_jobs-offers-table.scss", "hash": "5e5ffc8a0d15ad4ce3d8acf9eec4f1edffc00d0e"}, {"file": "apps/recruitments/styles/_joining-room-modal.scss", "hash": "24fed38a5219c1c9c5f8c5f0e8de8e19cd6c60ff"}, {"file": "apps/recruitments/styles/_live-coding.scss", "hash": "dfbfdbfa2123bb8966bc3fc6ab55b4ea2fcd51fb"}, {"file": "apps/recruitments/styles/_loader.scss", "hash": "18084c4fedea729e5eb0a52279dc8bd19d1443ef"}, {"file": "apps/recruitments/styles/_mail-box-screen.scss", "hash": "664a4be0d50214424b38146c0fc6d8211a6eff67"}, {"file": "apps/recruitments/styles/_manage-assignment.scss", "hash": "5477318eb5decb765852ce885db65b7e10351ea6"}, {"file": "apps/recruitments/styles/_manage-team-popup.scss", "hash": "038ff598c3c4dc6a0b3144843064694328c2255f"}, {"file": "apps/recruitments/styles/_manage-team.scss", "hash": "c90f6a7f5d93208a6a09822ee5105ed49a739464"}, {"file": "apps/recruitments/styles/_mixins.scss", "hash": "98884a1943dc5abc32bd7ba8dd99066a92570f70"}, {"file": "apps/recruitments/styles/_offer-preview.scss", "hash": "a4837a3365b83129d37e258d9f38bd54a50c4f26"}, {"file": "apps/recruitments/styles/_popups.scss", "hash": "d9dfd243c8ab40d9d9c4d2ccb355ac973fb3d469"}, {"file": "apps/recruitments/styles/_progress.scss", "hash": "c7c619000d21485c08e03fde6ac2f39d5a17d27d"}, {"file": "apps/recruitments/styles/_question_popup.scss", "hash": "73fde6c3a51aaa75dca6cbf5672add0af906b59a"}, {"file": "apps/recruitments/styles/_react-quill.scss", "hash": "6a09ab88da9e3440c1be853faccf121eade0e19d"}, {"file": "apps/recruitments/styles/_recruitment.scss", "hash": "60248f45807159810e83109e163970ab7f20a2c5"}, {"file": "apps/recruitments/styles/_schedule.scss", "hash": "580b9413fbd9ec80be20446a856b1213d6f691f9"}, {"file": "apps/recruitments/styles/_scoreboard.scss", "hash": "eabddaba36c7822f5a9601ecee96e3920da22cc9"}, {"file": "apps/recruitments/styles/_signature-iframe.scss", "hash": "9af30171146164686194f0b5e4fd0c290471a59a"}, {"file": "apps/recruitments/styles/_small-loader.scss", "hash": "492b60de9ddf214caa8b1f872d5546c539575993"}, {"file": "apps/recruitments/styles/_user-information-screen.scss", "hash": "481eaff269a1ebb0a889d0060457ec468453edc8"}, {"file": "apps/recruitments/styles/_users-offers-table.scss", "hash": "3541ee488bc92c70e692ef6bf6411acf3e77fdb2"}, {"file": "apps/recruitments/styles/_videoMeetLoader.scss", "hash": "fca704efe9e6091fe90ac85cfeb7624c88950373"}, {"file": "apps/recruitments/styles/_view-screening-report.scss", "hash": "0c7b362377f1e62890e6233dfb476c141a8780be"}, {"file": "apps/recruitments/styles/_workflow-chatbot.scss", "hash": "4fea1ea4be1b403306b67517abd62eb46a2a9cc5"}, {"file": "apps/recruitments/styles/_workflow.scss", "hash": "36a5be2aec0be0bd5008644a862efc13f6f8ad82"}, {"file": "apps/recruitments/styles/answerSheet/_answerSheetScreen.scss", "hash": "4bd3e85439b2f9ed8b8a5599b2fe57ed17259b40"}, {"file": "apps/recruitments/styles/answerSheet/_question.scss", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/recruitments/styles/datepicker/datepicker.scss", "hash": "4142654d4891dc36dd6ef1426c27d95f73d74864"}, {"file": "apps/recruitments/styles/datepicker/mixins.scss", "hash": "255978e34dbc387795d3b9cda56a2f629c61390c"}, {"file": "apps/recruitments/styles/datepicker/variables.scss", "hash": "48109e34218223e6e229f60f29339fea9a17b78e"}, {"file": "apps/recruitments/styles/jobs-table.scss", "hash": "1d25f56d241ddc2038b4cd71b97dff7c4f70d317"}, {"file": "apps/recruitments/styles/main.scss", "hash": "1b08cb8cb438bc80d437b904c66947013ee108f5"}, {"file": "apps/recruitments/styles/offerLetter/_modal.scss", "hash": "56eec109fe981a3da10ec31df4cc23b9b990df0d"}, {"file": "apps/recruitments/styles/offerLetter/_offer-view.scss", "hash": "9ac173f30dd4cad92d47d86332b38cfb13008f74"}, {"file": "apps/recruitments/styles/selectCustomBottomStyle.ts", "hash": "6d560e9f483bdef8ef72076c01ccf5de5f9b99b8"}, {"file": "apps/recruitments/styles/selectCustomErrorStyle.ts", "hash": "f013a863158e4a270c7733101787a3690c6224da"}, {"file": "apps/recruitments/styles/selectCustomStyle.ts", "hash": "0db35de28e57bb7bde1832c2e924adc35bae7fe2"}, {"file": "apps/recruitments/styles/selectDisableStyle.ts", "hash": "118b5a21cd0ecc90b4789162d4e857ec01cde76a"}, {"file": "apps/recruitments/styles/selectMobileMenuStyle.ts", "hash": "13ff3678aeb4d0ec44aa3867b15621f89fedeb03"}, {"file": "apps/recruitments/styles/selectSmallStyle.ts", "hash": "26cf49d9ec24223905cd013bc79dae54d037bb04"}, {"file": "apps/recruitments/tsconfig.json", "hash": "4e20af5c22c6334824a543a35f728f1a4ee2a5f6"}, {"file": "apps/recruitments/types/global/global.ts", "hash": "bd2e7d8930982b50f79e360f3befd04059c93c23"}, {"file": "apps/recruitments/types/redux/answer-sheet.ts", "hash": "6a18ed41279504087439b0c9b07e16f1a08d8240"}, {"file": "apps/recruitments/types/redux/assignment-candidate.ts", "hash": "6448e76123bb820794d86d8487e8fae61c592759"}, {"file": "apps/recruitments/types/redux/background-screening.ts", "hash": "30049074d635b71948862fb21631f4a44e259aaa"}, {"file": "apps/recruitments/types/redux/candidate-domain-assessment.ts", "hash": "ececea29de96445735447470a59377d81c1c450d"}, {"file": "apps/recruitments/types/redux/candidate-screen.ts", "hash": "25affcde50063cb33c24ebcba294c043007752fa"}, {"file": "apps/recruitments/types/redux/domain-assessment.ts", "hash": "d07ed9694045b5708e1a637e38ed8defee46db41"}, {"file": "apps/recruitments/types/redux/domain-review-and-score.ts", "hash": "2b63cb93069b0ca1a1cee0bd925c2237a3fdedcc"}, {"file": "apps/recruitments/types/redux/drug-screening.ts", "hash": "dbee1bc7d9b0e60d3434c53a268358d9f61fb0cd"}, {"file": "apps/recruitments/types/redux/generate-offer-screen.ts", "hash": "e7dfd28cd4f27151651661922a15087b47fde333"}, {"file": "apps/recruitments/types/redux/interviews.ts", "hash": "686b7cd7671fd73184209aac4177f55db9a54dda"}, {"file": "apps/recruitments/types/redux/job-detail-view.ts", "hash": "0905f9cf8b8fb6ff991f25772c773ed47713fa20"}, {"file": "apps/recruitments/types/redux/jobs-offers.ts", "hash": "434187db3a855b3eb97681681da4b8c5d1e24585"}, {"file": "apps/recruitments/types/redux/jobs.ts", "hash": "ac675eb6d91363ba3238865207fb0be646d555c2"}, {"file": "apps/recruitments/types/redux/live-coding.ts", "hash": "8bb2ae085983de5e6e6039329cf19dd1564b1ef9"}, {"file": "apps/recruitments/types/redux/manage-assignment.ts", "hash": "7feb36a14090433b5c69744bceaa8ef8e4a166f8"}, {"file": "apps/recruitments/types/redux/manage-candidate.ts", "hash": "efc6df6fe6d8dacf7e198535fd67e0affd694362"}, {"file": "apps/recruitments/types/redux/manage-team-members.ts", "hash": "e4ce314e2b7ee4fe25dd0b9de56a353ae685d7cf"}, {"file": "apps/recruitments/types/redux/offer-letter.ts", "hash": "3020ce08ebc546db9d019bc83aeb1574fa97a046"}, {"file": "apps/recruitments/types/redux/position-workflow.ts", "hash": "cb376febbd4bdfefec8884870677afcaf659a715"}, {"file": "apps/recruitments/types/redux/review-and-score.ts", "hash": "c69153897accac206e979e71272fc89e07f2182b"}, {"file": "apps/recruitments/types/redux/scoreboard.ts", "hash": "4222354be66e380dd793833bbe49fae0687e9a0c"}, {"file": "apps/recruitments/types/redux/users-offers.ts", "hash": "44bbc59ceafb7cad1ac530fe4144423ec7ae8253"}, {"file": "apps/recruitments/utils/checkLastAddedItemToSelect.ts", "hash": "60906bdbaf7df4199c3a15b016588e1e300664be"}, {"file": "apps/recruitments/utils/constants.ts", "hash": "be7893e5f2d574739ac572bdde2dcfeea15dbed4"}, {"file": "apps/recruitments/utils/index.ts", "hash": "9a243c75c06dc7c610bd24a53edb5e784313003e"}, {"file": "apps/recruitments/utils/materialTheme.ts", "hash": "91ae54b9141cb26ad4d738cf0238f6ad736018a1"}, {"file": "apps/recruitments/utils/monaco/monacoLanguageMiddlewareFunc.ts", "hash": "05f339186d46cdd2581b5009c03dfbe6f7421c94"}, {"file": "apps/recruitments/utils/monaco/monacoPostEditor.ts", "hash": "4beb7917dbf42824bbdbbb9e17b88f1b521b7631", "deps": ["api"]}, {"file": "apps/recruitments/utils/monaco/themes.tsx", "hash": "24a59996e91721f4542d93ba35f713c00fbd8ce0"}, {"file": "apps/recruitments/utils/rounds/checks.ts", "hash": "927f744d0dec16ccbaad6ea2cb0852438705d325"}, {"file": "apps/recruitments/utils/rounds/index.ts", "hash": "4a822ad1440efac4e55470e9c9f6975e7dac95a5"}, {"file": "apps/recruitments/utils/rounds/searchActive.ts", "hash": "4ba00626512bdc36c55a97c951ba55584cefbc18"}, {"file": "apps/recruitments/utils/rounds/searchRound.ts", "hash": "1122a7b12ca952247cf78662e51aff9ea9fbd7ac"}, {"file": "apps/recruitments/utils/sortingFuncForTables.ts", "hash": "5e1b0b21dddab470160821f23323161dae50461f"}, {"file": "apps/recruitments/utils/textUtils.ts", "hash": "250321d4d0ffe6795fbf6ce61ed82329e62f28b9"}, {"file": "apps/recruitments/utils/transformDate.ts", "hash": "ff8a57b198d615d50794b2a5d94452ee0c0f415a"}, {"file": "apps/recruitments/webpack.config.js", "hash": "15538e4a37f8b849ca75347f9f291606f7086151"}]}}, "@ucrecruits/globalstyle": {"name": "@ucrecruits/globalstyle", "type": "lib", "data": {"root": "apps/globalstyle", "sourceRoot": "apps/globalstyle", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/globalstyle/.eslintrc", "hash": "be8b560b3cfb73c336416f85a7198885a2d5e697"}, {"file": "apps/globalstyle/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/globalstyle/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/globalstyle/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/globalstyle/components/AuthProvider/AuthGuard.tsx", "hash": "7b62b9d8cd260a595268b18fbd1fe6f4adc4edd0"}, {"file": "apps/globalstyle/components/AuthProvider/AuthProvider.tsx", "hash": "4d2c59e31c6c0504a03149521f127a1f54d736f2"}, {"file": "apps/globalstyle/components/AuthProvider/index.ts", "hash": "7b2976188df145a250d84267c1cd6a065ee268f5"}, {"file": "apps/globalstyle/components/AuthProvider/useAuth.ts", "hash": "81bbaafea1f0858d2675f3a7621f3f08235291c6"}, {"file": "apps/globalstyle/components/AuthProvider/useHasPermission.ts", "hash": "4d310f7b97b1471545f24a7a770be9612584cd8e"}, {"file": "apps/globalstyle/components/ButtonComponent/button.scss", "hash": "6efeed06ba1f93a0d49636caddd0b57dcff8e21e"}, {"file": "apps/globalstyle/components/ButtonComponent/ButtonComponent.tsx", "hash": "356f3a58b3d097d2e58c581a30acf060362c8c34"}, {"file": "apps/globalstyle/components/CheckBox/_checkbox.scss", "hash": "47c95c527f34fcdbd37a40a7072587b68abc2262"}, {"file": "apps/globalstyle/components/CheckBox/CheckBox.tsx", "hash": "b9d0dccd2cc3919f51315cb7d4698009ce126085"}, {"file": "apps/globalstyle/components/CustomRange.tsx", "hash": "87ef1a1fe75defb51ef479a64d7f536e7d67dd15"}, {"file": "apps/globalstyle/components/DashboardEmptyData.tsx", "hash": "d315c1a95e347ef36a50f2b7ff694c3c49849ee6"}, {"file": "apps/globalstyle/components/DomainAssessmentResult/DomainAssessmentResult.tsx", "hash": "3ddc777b03ae4a050eb5545a67b4895d36a1c500"}, {"file": "apps/globalstyle/components/FallbackPages/DesktopOnlyFallback.tsx", "hash": "1fd15384f6664195b7d1db83d3c7a80eab679074"}, {"file": "apps/globalstyle/components/FallbackPages/FallbackComponent.tsx", "hash": "1b2c9a13c0339ef20c2fa2171d885251eacd3b17"}, {"file": "apps/globalstyle/components/FallbackPages/NoActiveSubscriptionPage.tsx", "hash": "fc24084ff6b7c4835e78a5486be113bcdd3564e5"}, {"file": "apps/globalstyle/components/FallbackPages/NotFoundPage.tsx", "hash": "44a00225e5f7728bfde0fcfa2ef89687c61065d4"}, {"file": "apps/globalstyle/components/FallbackPages/UnauthorizePage.tsx", "hash": "3d0580bee1d3ea5f3e6464773a42acb0cb460037"}, {"file": "apps/globalstyle/components/FallbackPages/UpgradeSubscriptionPage.tsx", "hash": "9345e12c910adf2c7676b8374e23d629bd56ebc1"}, {"file": "apps/globalstyle/components/FilterWithLimit/FilterWithLimit.tsx", "hash": "0cb90fd5195d094144b0348fc3d07abf7f1df9a7"}, {"file": "apps/globalstyle/components/FilterWithLimit/Option.tsx", "hash": "9b1a8292fd8784be1a37d0047c1f0c43afbb6073"}, {"file": "apps/globalstyle/components/LocationSelect/SelectComponent.tsx", "hash": "cd8172e9d558c4fef46adbdec07e61cfa9d49354"}, {"file": "apps/globalstyle/components/LocationSelect/SelectLocation.tsx", "hash": "9ff9e5df0de0865877cf84cefba1c083075fe513"}, {"file": "apps/globalstyle/components/ReportsComponents/AnalyticsAreaChart.tsx", "hash": "88e2ffff8ca1f309fda8d796dbc0a0776a696f8c"}, {"file": "apps/globalstyle/components/ReportsComponents/AnalyticsCircleChart.tsx", "hash": "****************************************"}, {"file": "apps/globalstyle/components/SearchField/_search.scss", "hash": "384ade00f6c43d8082553418cd4b2bb04edf164b"}, {"file": "apps/globalstyle/components/SearchField/SearchField.tsx", "hash": "6b70267d8c7b9e9b12e41ab05322e296909650fe"}, {"file": "apps/globalstyle/components/Subscription/index.ts", "hash": "308a0cb265ddf676d949c165f8ea3020f6dddd92"}, {"file": "apps/globalstyle/components/Subscription/SubscriptionGuard.tsx", "hash": "54f8116318b11968a75b4f1472312463dba8c76a"}, {"file": "apps/globalstyle/components/SVGs/ArrowSVG.tsx", "hash": "297dc8e5ae80b8f56fec3f5cf706c1a341aea66b"}, {"file": "apps/globalstyle/components/SVGs/Chatbot.svg", "hash": "2b6ebe54c8a0bac349a6c8bd04e2cc0288c795d7"}, {"file": "apps/globalstyle/components/SVGs/chatbotIcon.svg", "hash": "26486efcddd6ce537e90fde0c0efb28f2a043402"}, {"file": "apps/globalstyle/components/SVGs/ChatbotSVG.tsx", "hash": "021fa1134801ac1e54f040bda90d62ba594225dd"}, {"file": "apps/globalstyle/components/Table/CommonTheadItem.tsx", "hash": "ff1183f94a0385076808d012750afc49d1f45ada"}, {"file": "apps/globalstyle/components/Table/Filter/FilterWrapper.tsx", "hash": "5cdc7d94b73d45b55d96c2d2efb45ca6837a3396"}, {"file": "apps/globalstyle/components/Table/Filter/TopFilterButton.tsx", "hash": "8b142b13f8cf28a45365a9b28f7d1612f7a3aaaf"}, {"file": "apps/globalstyle/components/Table/ItemNotAvailable.tsx", "hash": "9d8515db150f88c13c3ef8fddc6dce9be2650b7a"}, {"file": "apps/globalstyle/components/Table/Loader.tsx", "hash": "b10b2295eafdf90075e4b905f6bdae9b802a27f2"}, {"file": "apps/globalstyle/components/Table/MemoOrderSVG.tsx", "hash": "10f534a5267771141100610d9ed94fb22ee7da72"}, {"file": "apps/globalstyle/components/Table/MobileTable/MobileTable.tsx", "hash": "54207fc71c05bbf59dfdfeb3ef27cf18521ae489"}, {"file": "apps/globalstyle/components/Table/MobileTable/MobileTableItem.tsx", "hash": "29ac3019038a5c1929726f88017f9361155993e0"}, {"file": "apps/globalstyle/components/Table/MobileTable/SingleMobileItem.tsx", "hash": "d88674be062d41d4ffc3fd58f9aaae1176dd55ac"}, {"file": "apps/globalstyle/components/Table/OrderPopup/DNDOrderPopupItem.tsx", "hash": "676790cb4ed437251d71cfbc1cdec7779d953336"}, {"file": "apps/globalstyle/components/Table/OrderPopup/DNDOrderPopupWrapper.tsx", "hash": "87a00d52af65939598fcdf3f9b7bc962ed3f0bb2"}, {"file": "apps/globalstyle/components/Table/OrderPopup/OrderPopupInner.tsx", "hash": "82f1953431062c42e2788020287ff7ba0d4d7367"}, {"file": "apps/globalstyle/components/Table/Pagination.tsx", "hash": "5c94035fc2e4019f128e5f4c5c25f1bcb1249b60"}, {"file": "apps/globalstyle/components/Table/SmallLoader.tsx", "hash": "257930618007c2c1cfa30aab51cabfcd72049d8b"}, {"file": "apps/globalstyle/components/Table/TableCardView/TableCardList.tsx", "hash": "49b3317596356a840e491b99415aefc02f6fc50d"}, {"file": "apps/globalstyle/components/Table/TableCardView/TableCardView.tsx", "hash": "23db2f1db919b22d69e36ce7ce7f1671916cf4e3"}, {"file": "apps/globalstyle/components/Table/TableCardView/TableView.tsx", "hash": "4fd930f4bc0cbadd7239461711113de2ecca2781"}, {"file": "apps/globalstyle/components/Table/TableEmpty.tsx", "hash": "d0caaf4e89453a4182bd1f5b496a31d7a3b48603"}, {"file": "apps/globalstyle/components/Table/TheadItem.tsx", "hash": "fc85debc2a93cb2e2933be8173626b6fa80addbd"}, {"file": "apps/globalstyle/components/Tooltip.tsx", "hash": "457d307bffaf326b4ef47aa9ea0ea67c31a38413"}, {"file": "apps/globalstyle/components/WarningBox.tsx", "hash": "7ce3e1952c233d9bd382f2d90ceec69ec725e22f"}, {"file": "apps/globalstyle/context/authContext.ts", "hash": "17812358d57afaa6fc11a32cb244757ab71e375e"}, {"file": "apps/globalstyle/context/subscriptionContextProvider.tsx", "hash": "972b377bb9b80b2a154585ef8f064d10e81438b1"}, {"file": "apps/globalstyle/context/types.ts", "hash": "e365b54d9dccc7f61618c5d24dc106b798e42622"}, {"file": "apps/globalstyle/hook/useMobilePagination.ts", "hash": "11f896816154fe00b95ecbb97bc43a438359209a"}, {"file": "apps/globalstyle/hook/usePagination.ts", "hash": "db3ca855880cf636cad6a0882045bd05a23e25d1"}, {"file": "apps/globalstyle/hook/useTableCardView.ts", "hash": "ea1d5a08192254bd3797319b0ebcecd5a131db3d"}, {"file": "apps/globalstyle/images/icons/Close.svg", "hash": "52fdcbd8986403fb52049057bf07378467013b06"}, {"file": "apps/globalstyle/images/icons/cross.svg", "hash": "746d1ad7cba2c65ad9d9da725304e2bb67dc3128"}, {"file": "apps/globalstyle/images/icons/delete_ic.svg", "hash": "9ca29516d488f6e4ebdb1a717a11938013003421"}, {"file": "apps/globalstyle/images/icons/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/globalstyle/images/icons/edit_ic.svg", "hash": "5f3b472b990252dbffb690826248bda2045ef414"}, {"file": "apps/globalstyle/images/icons/fallback/dashboard-loader.svg", "hash": "10024f2510c83094fd3df073bcc813e2f12c6a14"}, {"file": "apps/globalstyle/images/icons/fallback/desktop-compatible-fallback.svg", "hash": "ce4fd053b43525b968e6a16119fee69d0919f135"}, {"file": "apps/globalstyle/images/icons/fallback/none_subscription.svg", "hash": "a041037c9f08ad11144448c9b395c8c9f6357a3a"}, {"file": "apps/globalstyle/images/icons/fallback/unauthorized.svg", "hash": "21abdf209047f6fae8c4724823289b9ec09ecaa8"}, {"file": "apps/globalstyle/images/icons/fallback/upgrade-subscription.svg", "hash": "51b26f54bbd324676a9e1e3cc26dbb28da263ed3"}, {"file": "apps/globalstyle/images/icons/filter-arrows.svg", "hash": "179f6a9fd6dc2b6221d3b86566e14e576f99b5a7"}, {"file": "apps/globalstyle/images/icons/globe.svg", "hash": "32587f3bbb9b57110f02cbf3e2f226d315860526"}, {"file": "apps/globalstyle/images/icons/message.svg", "hash": "5d455de072382f402f3413eafaf57b3049b610f4"}, {"file": "apps/globalstyle/images/icons/no-data.svg", "hash": "b7baa1a754f35557402af798220062bf946aa702"}, {"file": "apps/globalstyle/images/icons/reset_ic.svg", "hash": "f7384a5d57ac044338e4ec8b7ed088ec38f2c952"}, {"file": "apps/globalstyle/images/icons/save_ic.svg", "hash": "ea61d1da58599e4dd91a8991d378ac396226cb58"}, {"file": "apps/globalstyle/images/icons/search_ic.svg", "hash": "885b2d6b457b5b51f2d2823e7da7d47fbd899164"}, {"file": "apps/globalstyle/images/icons/small_done_ic.svg", "hash": "8d1a6c4d06667a0bdddb5e33a7ebd211d68ab3b6"}, {"file": "apps/globalstyle/images/icons/table/empty-filter.svg", "hash": "4dfd50f0628089551d9d4427b0c415992a601fde"}, {"file": "apps/globalstyle/images/icons/table/empty-search.svg", "hash": "f3f308f1b83e66d88b296a62eee0aec610ac4422"}, {"file": "apps/globalstyle/images/icons/table/empty-table.svg", "hash": "67f8903755aa519965e3fa11bcdff2d832453852"}, {"file": "apps/globalstyle/images/icons/tick.svg", "hash": "4516088f928ef310795170a86a2a714c08952b9a"}, {"file": "apps/globalstyle/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/globalstyle/package.json", "hash": "b245be2a4223c48d5c815374d1c69d2af5e1adda", "deps": ["npm:typescript"]}, {"file": "apps/globalstyle/screen/OffersSign.tsx", "hash": "87c900c3eddba536ea75247b8489e411d66acd25", "deps": ["api"]}, {"file": "apps/globalstyle/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/globalstyle/src/gstyle.css", "hash": "81501ec00a582d4b9c95392c855d38c63f5c20a9"}, {"file": "apps/globalstyle/src/root.component.tsx", "hash": "20c4bc854dfa63dd20b2d7d47e9096c667fdbc79"}, {"file": "apps/globalstyle/src/ucrecruits-globalstyle.tsx", "hash": "ff53dce75c7a9e64bd78236cee07d79c4b9ca583"}, {"file": "apps/globalstyle/styles/_config.scss", "hash": "bbb22ca2c4134037993848c4462f5c4617925215"}, {"file": "apps/globalstyle/styles/_dashboard-empty-data.scss", "hash": "b4c41f8c51581d2176622687b033779b38e7be38"}, {"file": "apps/globalstyle/styles/_domain-assessment-result.scss", "hash": "132e6d35ddf5dacf6e712179616e2f1f5c59eb7b"}, {"file": "apps/globalstyle/styles/_empty-table.scss", "hash": "eff2d71c6f5b9ad84683bf98f7f64ac1e6e8671c"}, {"file": "apps/globalstyle/styles/_fallback-pages.scss", "hash": "bf6ff0b2844c82c1df304f56990eb6e72be215d2"}, {"file": "apps/globalstyle/styles/_loader.scss", "hash": "0ce94104242b8a8c8286bf42b68b36349e63eb1e"}, {"file": "apps/globalstyle/styles/_mixins.scss", "hash": "57848f4b27725f2dd86256daf5d3bd4cde60a04a"}, {"file": "apps/globalstyle/styles/_small-loader.scss", "hash": "d0de60c19ca401934125a790bd9414c548549558"}, {"file": "apps/globalstyle/styles/_table-card.scss", "hash": "6a8ea3ca4278405b46738528ceee06e533b01ce0"}, {"file": "apps/globalstyle/styles/_table.scss", "hash": "df78e6fbbe18acc89f780f16763da0a50ce73ce8"}, {"file": "apps/globalstyle/styles/filter/selectMobileMenuStyle.ts", "hash": "13ff3678aeb4d0ec44aa3867b15621f89fedeb03"}, {"file": "apps/globalstyle/styles/main.scss", "hash": "c342c34845ee17dea2de14b14c921dd24377812c"}, {"file": "apps/globalstyle/styles/main/_fonts.css", "hash": "85d7a72c017dbeea338788ce6c4e46f4827046b2"}, {"file": "apps/globalstyle/styles/main/_global.scss", "hash": "5c0f9118a60274bc2a3d3634d847d366430961b3"}, {"file": "apps/globalstyle/styles/main/_reset.scss", "hash": "2559e8d4e15fce7912d629f29b748b89132f3ff2"}, {"file": "apps/globalstyle/styles/selectCustomStyle.ts", "hash": "631040ffab2e6de8a025b61521ad8083f01e9872"}, {"file": "apps/globalstyle/styles/selectSmallStyle.ts", "hash": "147446a4e98f42fe6c7b0b1e786d6dae53fadf01"}, {"file": "apps/globalstyle/tsconfig.json", "hash": "39159d7b81b0b105eacfe65496e9af14ff4efd0b"}, {"file": "apps/globalstyle/types/analytics-table-types.ts", "hash": "b4951a0359a6352abbdc42162e4a8e2841ba4649"}, {"file": "apps/globalstyle/types/button-type.ts", "hash": "58abc052aea851f9e7edbb6f220482172f3b9d36"}, {"file": "apps/globalstyle/types/domain-assessment-result-types.ts", "hash": "21fec1738b3d6622c94e21fcd11499a3cd78c7d0"}, {"file": "apps/globalstyle/types/location-filter-types.ts", "hash": "9a0184bbc823a536b58283c4a98bc190e2efb788"}, {"file": "apps/globalstyle/types/select-location.ts", "hash": "63b96dee7f9ef287e00bd17a0478cdef2d6e6687"}, {"file": "apps/globalstyle/types/table-types.ts", "hash": "00f847e263a1c0a60cbed3f24cdf5ddbc3f5856e"}, {"file": "apps/globalstyle/types/tooltip-types.ts", "hash": "87baedd78ad6a3af8ddd0353aad01c01a9e594ac"}, {"file": "apps/globalstyle/utils/getConfigForAuthorization.ts", "hash": "c1334ba9339cac013f5d1e7bc91468ce48676838"}, {"file": "apps/globalstyle/utils/getCountryStateCity.ts", "hash": "696c46f41891c51a011da6355daaebcda3e41523", "deps": ["npm:country-state-city"]}, {"file": "apps/globalstyle/utils/utils.ts", "hash": "e78b9095efe7cf0d753da3f4574404e00f83a1c5"}, {"file": "apps/globalstyle/webpack.config.js", "hash": "f83a3c92f2d398455c818b7cff68ebf09ed63363"}]}}, "root-config": {"name": "root-config", "type": "lib", "data": {"root": "apps/root-config", "sourceRoot": "apps/root-config", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/root-config/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/root-config/.prettierignore", "hash": "9f22a028245d31920b4f37941fff31626d024c5b"}, {"file": "apps/root-config/babel.config.json", "hash": "93f75b02a15f82e879329fcb89e0c0c858be38b3"}, {"file": "apps/root-config/package.json", "hash": "faa5cff55029f3d9154277decd7090e144deb842", "deps": ["npm:typescript"]}, {"file": "apps/root-config/public/monaco-workers/css.worker.js", "hash": "fa7a4063b36359b968065c6ba078156207a544fe"}, {"file": "apps/root-config/public/monaco-workers/css.worker.js.map", "hash": "79272ab2754e8957afeaefbb4ffcfa360cad448d"}, {"file": "apps/root-config/public/monaco-workers/editor.worker.js", "hash": "c98e98d7c324f3205da72fcd84f201c698fd8cdc"}, {"file": "apps/root-config/public/monaco-workers/editor.worker.js.map", "hash": "71ee4eb3640a0b45f59d034a3d3e6e851edbe1b3"}, {"file": "apps/root-config/public/monaco-workers/html.worker.js", "hash": "9806cd4294baf0752e1756d78dcd8a657a75a420"}, {"file": "apps/root-config/public/monaco-workers/html.worker.js.map", "hash": "37b4bc075c10451cc83cb1cbcc6d0c94e04282de"}, {"file": "apps/root-config/public/monaco-workers/json.worker.js", "hash": "df8a8276c7f1948e8247bd2d69f462f4dfb2d55e"}, {"file": "apps/root-config/public/monaco-workers/json.worker.js.map", "hash": "f449fe76bb1e663676a4b742e5f8249fae9415c2"}, {"file": "apps/root-config/public/monaco-workers/ts.worker.js", "hash": "89cc3417203c4681a1aed2113434ca1b6f4504e0"}, {"file": "apps/root-config/public/monaco-workers/ts.worker.js.map", "hash": "f1c8124c00552a287e4520b99e19cecd78ccc3de"}, {"file": "apps/root-config/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/root-config/src/firebase-messaging-sw.worker.js", "hash": "229a932e61bc11cea17a7f84edf633d98ce42d2b"}, {"file": "apps/root-config/src/firebase.js", "hash": "71c9198c0b2db316f49422caec69757392babfe9"}, {"file": "apps/root-config/src/index.ejs", "hash": "e7999057d7e76d49a3d0f0317f54563a49a92130"}, {"file": "apps/root-config/src/urecruits-root-config.ts", "hash": "e1e088825fcf4efe2d1202ebbb4ca75820837367"}, {"file": "apps/root-config/tsconfig.json", "hash": "fdcb0d4ab50c109e4a3c8c1973e6913691431a28"}, {"file": "apps/root-config/webpack.config.js", "hash": "ad981c56ca72d8dcd7ce758db79267eba9525245"}]}}, "candidates": {"name": "candidates", "type": "lib", "data": {"root": "apps/candidates", "sourceRoot": "apps/candidates", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/candidates/.eslintrc", "hash": "be8b560b3cfb73c336416f85a7198885a2d5e697"}, {"file": "apps/candidates/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/candidates/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/candidates/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/candidates/components/AboutCompany/HotJobs.tsx", "hash": "94df1ff06aea65624a776594217235f54af4ed5c"}, {"file": "apps/candidates/components/CalendarDayView/CalendarDayView.tsx", "hash": "535c4b3fef6e0113987beea329a27fc8b9c305a2", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/CalendarDayView/EventView.tsx", "hash": "15c443557fb65e02dbe387ef4fb557b6b5218f01", "deps": ["api"]}, {"file": "apps/candidates/components/CalendarFullView/ProviderErrorModal.tsx", "hash": "c55471b72588b6a1788ff4049297832e97fe3500", "deps": ["api"]}, {"file": "apps/candidates/components/candidate-assessment/CountdownTimer.tsx", "hash": "ac531aed81ce010de3fb98bd92874106b8e713ce"}, {"file": "apps/candidates/components/candidate-assessment/InstructionDialog.tsx", "hash": "1ca0c05d51ed51758e47aee0ce49949102e0af8e", "deps": ["api"]}, {"file": "apps/candidates/components/candidate-assessment/Pagination.tsx", "hash": "3b745e62deaf398a204900e95fbb9e55d941188a"}, {"file": "apps/candidates/components/candidate-assessment/Popups/editor-modals-text.json", "hash": "468e1eaeaf990ed5ace2f2bab4d0b22d497304ef"}, {"file": "apps/candidates/components/candidate-assessment/Popups/editor-modals.tsx", "hash": "b3edbf634dee71da9d1fa8881cdf01525f3176fd", "deps": ["live-coding"]}, {"file": "apps/candidates/components/candidate-assessment/Popups/Modal.tsx", "hash": "efa88e518b9fa1ed5c593c5f799d0bdf2ed3637e", "deps": ["live-coding"]}, {"file": "apps/candidates/components/candidate-assessment/question.tsx", "hash": "8cbef7e4ceda5cd628a2e195343abd002aadd186"}, {"file": "apps/candidates/components/CandidateJobs/CandidateJobsFilter.tsx", "hash": "c2e86e3f22ee4aed616ec237f1a5651d3d7f6d53", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/CandidateJobs/CandidateJobsFilter/CandidateJobsFilter.tsx", "hash": "36a3ad7d6a76646dd60a4890b389a775a0c74689", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/candidates/components/CandidateJobs/CandidateJobsFilter/components/index.ts", "hash": "f826122cbbf476e7804c4664f124f5a9cc8a7671"}, {"file": "apps/candidates/components/CandidateJobs/CandidateJobsFilter/components/LocationFilter/index.ts", "hash": "217f358ca1b69172e4fcc577ce5eac4ffc685ea4"}, {"file": "apps/candidates/components/CandidateJobs/CandidateJobsFilter/components/LocationFilter/LocationFilter.tsx", "hash": "c3e9b0d1eb0bf84e65b5f8d40baf1cc9bcf805fb", "deps": ["api"]}, {"file": "apps/candidates/components/CandidateJobs/CandidateJobsFilter/index.ts", "hash": "168c7d2966904d0e91b87f5afa8354f6f8acf79b"}, {"file": "apps/candidates/components/CandidateJobs/List/TableList.tsx", "hash": "06a107d65c0171a4acb034ef218ac25bb758ec7d", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/CandidateJobs/List/TableListItem.tsx", "hash": "d35e6256eb8d597940a08a62d1a8c71561d16530"}, {"file": "apps/candidates/components/CandidateJobs/MobileTable/MobileTable.tsx", "hash": "9748b7593f106f1a3e2e98b15d273a93877eae66"}, {"file": "apps/candidates/components/CandidateJobs/MobileTable/MobileTableItem.tsx", "hash": "a5687997b2a027e404fe83d45d005ddcac337da7", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/CandidateJobs/Popups/ActionPopupCandidateOffers.tsx", "hash": "5f4d6c6196897932d86fd137b3e6f6f85326b8b9"}, {"file": "apps/candidates/components/CandidateJobs/Popups/ApplyPopupCandidateJobs.tsx", "hash": "2c89cc9e3e06f0d43f72141cfe0d7c6cf5968657"}, {"file": "apps/candidates/components/CandidateJobs/Popups/SuccessApplyCandidateJobsPopup.tsx", "hash": "50c1e1497c2687cbc7b19b25703591b781c5981a"}, {"file": "apps/candidates/components/CandidateJobs/TBody/TbodyInnerCandidateJobs.tsx", "hash": "98c097a45586c4ea4eacdc4a23b19dda4578a228", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/CandidateScoreboard/Popups/DomainResultModal.tsx", "hash": "c39c242f4039ffab936219bc013b9c6e0b076563"}, {"file": "apps/candidates/components/Companies/List/TableList.tsx", "hash": "df79c84996eafdc95da9a427123a23e23eff9978"}, {"file": "apps/candidates/components/Companies/Table/MobileTable/MobileTable.tsx", "hash": "7184054e6521bdc4c4dca12e22b29331e2dbeb1c"}, {"file": "apps/candidates/components/Companies/Table/MobileTable/MobileTableItem.tsx", "hash": "5f51c3efc38ec8c67dc245dd873a5de3fb5bc675"}, {"file": "apps/candidates/components/Companies/Table/TBody/TBodyInnerCompanies.tsx", "hash": "028b4d49890835d2aaf38039ef6f06dd11e15e1f"}, {"file": "apps/candidates/components/CompanyJobs/CompanyJobsFilter/CompanyJobsFilter.tsx", "hash": "1e80474d33678a8d774d03654d12e645e8dbefda", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/candidates/components/CompanyJobs/CompanyJobsFilter/components/index.ts", "hash": "bffd4f7512bf4203f96091710e4f13b137e6fa65"}, {"file": "apps/candidates/components/CompanyJobs/CompanyJobsFilter/components/LocationFilter/index.ts", "hash": "bffd4f7512bf4203f96091710e4f13b137e6fa65"}, {"file": "apps/candidates/components/CompanyJobs/CompanyJobsFilter/components/LocationFilter/LocationFilter.tsx", "hash": "83b879bb1a94eddf8eb1a37c4650d9f7c4080298", "deps": ["api"]}, {"file": "apps/candidates/components/CompanyJobs/CompanyJobsFilter/index.ts", "hash": "0c71aaeb7e7fb6b1d35d530751393540fee59a53"}, {"file": "apps/candidates/components/CompanyJobs/JobsHead.tsx", "hash": "d7c7b0e1ac7325c37b24cf316e2f325a50e9fd0f"}, {"file": "apps/candidates/components/CompanyJobs/List/TableList.tsx", "hash": "f9095907133058d78a9ce3cbbd6b02bca42e4df7", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/CompanyJobs/MultySelect/MultiSelect.tsx", "hash": "32797ed0055808aa68a19a085569a7e7f70bb453", "deps": ["api"]}, {"file": "apps/candidates/components/CompanyJobs/MultySelect/MultiSelectListItem.tsx", "hash": "7bef51776feb9ffdf65e0cc731c6b854ce400541"}, {"file": "apps/candidates/components/CompanyJobs/Table/MobileTable/MobileTable.tsx", "hash": "dc4f81ef530b1633ae3a4679073867bb892ed772"}, {"file": "apps/candidates/components/CompanyJobs/Table/MobileTable/MobileTableItem.tsx", "hash": "01758b472cf5a2fcba26e9f80e3d4a49ded748de", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/CompanyJobs/Table/Popups/ApplyPopup.tsx", "hash": "138b274554896930bd6ef3da9b1ebf54c0a16941"}, {"file": "apps/candidates/components/CompanyJobs/Table/TBody/TbodyInnerJobs.tsx", "hash": "92eef8af0728f44b065b3acaaf825beae6e28fab", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Dashboard/AppliedJobsTable.tsx", "hash": "e73730262e6c59bbd21863949bc1055b53ebbfe9", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Dashboard/AreaChart.tsx", "hash": "3f1fd271a3c208d000ce0df357e4ee63c25e79fc"}, {"file": "apps/candidates/components/Dashboard/CircleChart.tsx", "hash": "****************************************"}, {"file": "apps/candidates/components/Dashboard/DashboardDropdown.tsx", "hash": "ad79e60b8d78f8ccebe1404e4ec59e4dd48812bd"}, {"file": "apps/candidates/components/Dashboard/Employess.tsx", "hash": "181d9d1e33260afbd65de990ed94151fd83377ea", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Dashboard/JobMatches.tsx", "hash": "1cdcfaee5827f913977f6755dabe8510a4099150", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Dashboard/LineChart.tsx", "hash": "3de38260dd7bb4ff8f8d546d6c6c0855bd52cfe4"}, {"file": "apps/candidates/components/Dashboard/PendingTask.tsx", "hash": "16177ea4867f0b96e700ee60d99f79526e0fe877", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Dashboard/PostedJobTable.tsx", "hash": "dcca6c714bb5d3acc2611bc6bdd0f9dd3de40760", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Dashboard/ProgressCircle.tsx", "hash": "****************************************"}, {"file": "apps/candidates/components/Dashboard/RescheduleTasks.tsx", "hash": "76b8f031cd7e0d597ba889649a4b1167a223be59", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Dashboard/TotalCount.tsx", "hash": "1ae22febae69461190648b07536526b6686feb33", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/candidates/components/Global/SelectOptions.tsx", "hash": "9b1a8292fd8784be1a37d0047c1f0c43afbb6073"}, {"file": "apps/candidates/components/Global/table/FilterWrapper.tsx", "hash": "967bb3a80ad6cd75e7fd49b6b659fac1605863b4"}, {"file": "apps/candidates/components/Global/table/MemoOrderSVG.tsx", "hash": "10f534a5267771141100610d9ed94fb22ee7da72"}, {"file": "apps/candidates/components/Global/table/NoResultsSearch.tsx", "hash": "0c72a5954d9c83ea2e77884071ee18905c217b1c"}, {"file": "apps/candidates/components/Global/table/OrderPopup/DNDOrderPopupItem.tsx", "hash": "6b9eefb38b817b1def9bae88d7f8d8fa62dd24f8"}, {"file": "apps/candidates/components/Global/table/OrderPopup/DNDOrderPopupWrapper.tsx", "hash": "2f73a78e0106037fb038e74338e8c14c46127475"}, {"file": "apps/candidates/components/Global/table/OrderPopup/OrderPopupInner.tsx", "hash": "742db87ca33f2d781622d01543ff05330b85134f"}, {"file": "apps/candidates/components/Global/table/SingleMobileItem.tsx", "hash": "85eb411f118ffd52662c04bf7146752b4f2e163c"}, {"file": "apps/candidates/components/Global/table/TableEmpty.tsx", "hash": "de9f6f1798d95769b558b69f2f610b54735e3a1a"}, {"file": "apps/candidates/components/Global/table/TopFilterButton.tsx", "hash": "1b3553287fdc4f08d77bc9b831fc720aa93b4c8a"}, {"file": "apps/candidates/components/HOCs/OrderPopupHOCs.tsx", "hash": "edfb794ce1a8ee10ce0edc4da2afe98fa59ed9e5"}, {"file": "apps/candidates/components/LayoutCompany.tsx", "hash": "6dcd89c0aa37abdc010b60276aebd20ee4e4520c", "deps": ["api"]}, {"file": "apps/candidates/components/SVG/AcceptSVG.tsx", "hash": "de7bacf17774b6773bbefff7342ebb3bad144bca"}, {"file": "apps/candidates/components/SVG/ApplySVG.tsx", "hash": "bdb50145dfd78c6c0be8fbf5755547b5892630f5"}, {"file": "apps/candidates/components/SVG/CloseSVG.tsx", "hash": "64ee7ea9a65911de80c8597bb88a7cbc9b938de1"}, {"file": "apps/candidates/components/SVG/ListSVG.tsx", "hash": "a49ba93823994f4768571823c2bd2a45905307ed"}, {"file": "apps/candidates/components/SVG/RejectSVG.tsx", "hash": "00ceb5df6be528018e3f486ee50da2aaeefbbe35"}, {"file": "apps/candidates/components/SVG/SaveSVG.tsx", "hash": "e31a4bb9a5e128b88a4d7cf81107fa20dedcf23a"}, {"file": "apps/candidates/components/SVG/SeeApplicationFormSVG.tsx", "hash": "85e835281e84e3dea574f3bc2434e90ff20cba26"}, {"file": "apps/candidates/components/SVG/TableSVG.tsx", "hash": "76b2eac527401a8f497ee434803fa9eba3aab1eb"}, {"file": "apps/candidates/components/SVG/ViewSVG.tsx", "hash": "2cc045cd60a8f67a941208b7a36d12376c2b8195"}, {"file": "apps/candidates/enums/CandidateAssessmentTypeEnums.ts", "hash": "017794e30524635891cd23b61beb32cbd9e1a693"}, {"file": "apps/candidates/enums/CandidateJobsEnums.ts", "hash": "2ac94caf2d70ba8d2fee39e58bbd7661f7e5de76"}, {"file": "apps/candidates/enums/CompaniesTableEnums.ts", "hash": "00408cc2bc8e1b80a2297c33de6e27c3ab287682"}, {"file": "apps/candidates/enums/CompanyJobsTableEnums.ts", "hash": "42e1fd208df59909b3498ee57e9a68943de1ebd2"}, {"file": "apps/candidates/enums/index.ts", "hash": "8f8febce59f4de5efff0867a81443af5cf76433f"}, {"file": "apps/candidates/enums/Rounds.enum.ts", "hash": "5698338397cf76827707a3fef31432837f4320d5"}, {"file": "apps/candidates/hook/fetchData.ts", "hash": "3a06147dad9e5f451003ecb20ab3aadabe295713", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/candidates/hook/numberWithCommas.tsx", "hash": "bde4064662b852e23dbd66ed9eb1afbca664ef18"}, {"file": "apps/candidates/hook/searchFunc.ts", "hash": "18429bb996b3c9d80a9f85a17cc4c8791a441c20", "deps": ["api"]}, {"file": "apps/candidates/hook/useClickOutside.ts", "hash": "7fe6045f7460948ae1e0816013bc5f1e8c449321"}, {"file": "apps/candidates/hook/useTableClickAndDragScroll.tsx", "hash": "b186aee26af74a8a4ebb4d606e8b763e1ae9ea17"}, {"file": "apps/candidates/hook/validateEmail.ts", "hash": "8e558e101fe4d056b15d28cd46fffc21bfea11eb"}, {"file": "apps/candidates/hook/validatePhoneNumber.ts", "hash": "e7e1e97fa068915a022144259f4e76abe6bfe570"}, {"file": "apps/candidates/image/dashboard-loader.svg", "hash": "10024f2510c83094fd3df073bcc813e2f12c6a14"}, {"file": "apps/candidates/image/icon/apply_ic.svg", "hash": "9fa182c446c7d2ff631b6d99876952563fa0c8f3"}, {"file": "apps/candidates/image/icon/approve_job_ic.svg", "hash": "88019e070c7a267a3e16aa7b6eb166179269804c"}, {"file": "apps/candidates/image/icon/avatar.svg", "hash": "a6dfdace3013cadec974a9b5e89b193268aa4c0a"}, {"file": "apps/candidates/image/icon/bookmark_blue_ic.svg", "hash": "d6e5346ba06787ae801189e7945aa501679a569b"}, {"file": "apps/candidates/image/icon/bookmark_ic.svg", "hash": "127293ad11f24ac5da99666dd72fb9250d841ed9"}, {"file": "apps/candidates/image/icon/calendar_ic.svg", "hash": "05f6771e1475c792445c092090645cc6b0dfc105"}, {"file": "apps/candidates/image/icon/check-mark_blue_ic.svg", "hash": "4dc8cf406ae914e537f676d75c632011caa658b5"}, {"file": "apps/candidates/image/icon/clock_ic.svg", "hash": "44160ad0388168ffc40d9fe2c8c456baee20ca74"}, {"file": "apps/candidates/image/icon/Close.svg", "hash": "52fdcbd8986403fb52049057bf07378467013b06"}, {"file": "apps/candidates/image/icon/delete_ic.svg", "hash": "9ca29516d488f6e4ebdb1a717a11938013003421"}, {"file": "apps/candidates/image/icon/delete_image_popup_ic.svg", "hash": "0b7567b7592f9d3280b705c6a2b9ccb07fdc9705"}, {"file": "apps/candidates/image/icon/done__inactive_ic.svg", "hash": "6b4094faa42c733d61c02a58cdabbc67331041e6"}, {"file": "apps/candidates/image/icon/done_ic.svg", "hash": "6b8deae0a63f19ad9d16d15b7498d62025a4200f"}, {"file": "apps/candidates/image/icon/done-tick.png", "hash": "81bfcb87d280e10cff8c8b0b9d3e51348782c044"}, {"file": "apps/candidates/image/icon/edit_ic.svg", "hash": "5f3b472b990252dbffb690826248bda2045ef414"}, {"file": "apps/candidates/image/icon/email-solid_green_ic.svg", "hash": "72cb678d0bd64888988bc13e98d2f4bb8257b043"}, {"file": "apps/candidates/image/icon/fire_ic.svg", "hash": "399c95144856c698c6f7d28b20009bb4d9e77dc8"}, {"file": "apps/candidates/image/icon/form-arrow_ic.svg", "hash": "7c79ca85c67951bbd9379a2de97ccf3ed6a8c2dc"}, {"file": "apps/candidates/image/icon/info_ic.svg", "hash": "2a47846f2e17314c5991014317880ccb2ded00db"}, {"file": "apps/candidates/image/icon/mail_green_ic.svg", "hash": "db492f98961da82fcd1b822684eb17768b24e242"}, {"file": "apps/candidates/image/icon/match.svg", "hash": "e532370aa48a696cb1d227bead65169073577acb"}, {"file": "apps/candidates/image/icon/no-data.svg", "hash": "b7baa1a754f35557402af798220062bf946aa702"}, {"file": "apps/candidates/image/icon/phone_green_ic.svg", "hash": "f0dad50739158114de548b8961ff08c6f6a44d54"}, {"file": "apps/candidates/image/icon/phone-solid_green_ic.svg", "hash": "690de097c4644efc1d3bf6e2cba11603d3fb7ba6"}, {"file": "apps/candidates/image/icon/plus_ic.svg", "hash": "cd242dafa9819a62487dac97e59122449808d6e0"}, {"file": "apps/candidates/image/icon/reject_job_ic.svg", "hash": "8a7dee66d7b908222401c701770e83ea95802ac2"}, {"file": "apps/candidates/image/icon/resent invite_ic.svg", "hash": "1728674f1046142420dfebab1641f7239e7ab031"}, {"file": "apps/candidates/image/icon/reset_ic.svg", "hash": "f7384a5d57ac044338e4ec8b7ed088ec38f2c952"}, {"file": "apps/candidates/image/icon/save_ic.svg", "hash": "ea61d1da58599e4dd91a8991d378ac396226cb58"}, {"file": "apps/candidates/image/icon/table/empty-table copy.svg", "hash": "67f8903755aa519965e3fa11bcdff2d832453852"}, {"file": "apps/candidates/image/icon/table/empty-table.svg", "hash": "eb692fef4136edbf3ad960e2cb8ef28153584033"}, {"file": "apps/candidates/image/icon/table/no-results copy.svg", "hash": "f3f308f1b83e66d88b296a62eee0aec610ac4422"}, {"file": "apps/candidates/image/icon/table/no-results.svg", "hash": "f3f308f1b83e66d88b296a62eee0aec610ac4422"}, {"file": "apps/candidates/image/icon/top-left-arrow_ic.svg", "hash": "24fb6dcf8b16d01922be7bd3536a6371b0a7a3ca"}, {"file": "apps/candidates/image/popup_success_ic.svg", "hash": "813569af0fe73e4dcf75faa2b541c392dd9499e9"}, {"file": "apps/candidates/image/temp_help-centr.png", "hash": "2647f91b888b7150444bc3d37baae72eea5e1c61"}, {"file": "apps/candidates/image/temp-company.png", "hash": "cea0c01ffdb9a5f49e128bc09a69ccdd3bccfe79"}, {"file": "apps/candidates/image/temp-user.png", "hash": "16418549d13ec89d7053b61c10fb42fe1de04e2d"}, {"file": "apps/candidates/image/Video.svg", "hash": "6fc58861233100c5c914728dc920d7f625e68e5c"}, {"file": "apps/candidates/image/watch1.svg", "hash": "4af8f969ec7e1b8b243872705ec41e99086a7c47"}, {"file": "apps/candidates/image/watch2.svg", "hash": "673ddd68c2064e6dbac6c21302b4490c7d6bcd29"}, {"file": "apps/candidates/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/candidates/package.json", "hash": "1ba728227d64f75c55e91c3de94bb1b6a2371cf5", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/candidates/screen/AboutCompany.tsx", "hash": "985c90bf033378785b4ce39d41bc692bc0aa38fd", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/AdminDashboard.tsx", "hash": "16a764cf7be82be39bd6cf99269fdeb185fc2a08", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/ApplicationForm.tsx", "hash": "db5567a84175ac69e446ba3a89b9c02cf1f14fb2", "deps": ["api", "recruitments", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/CandidateAnswerSheet.tsx", "hash": "5254dfc454f7dcf7ef6e2dabb7020aa925dcf9e9", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/CandidateDashboard.tsx", "hash": "cdd1b724374883fb5e1c08089a3816ec4fb30b64", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/CandidateJobScoreboard.tsx", "hash": "40aaced4a3b65ae4b3d5c64522a68c7dd0c151f6", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/CandidateOffers.tsx", "hash": "062e73b44f0ad58754674f837b84dcbd4b21ada8", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/CandidateOfferView.tsx", "hash": "6cf1e3265348187c209fd19e1677b1815e3a3d17", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/candidates/screen/CandidateScoreboard.tsx", "hash": "3c8bc9c2f1e081299734ae5040ce8eaa53f1cdd7", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/candidates/screen/Companies.tsx", "hash": "f9abccc97d8fff7e2585a84b2efcb4eff6d3ce9a", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/candidates/screen/CompanyJobs.tsx", "hash": "89fcfe73523a2d079e59a76d3f99037eb812b900", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/DomainAssessment.tsx", "hash": "f9135986486ed9c541aaf9812e9a03efb919d3ec", "deps": ["api", "recruitments"]}, {"file": "apps/candidates/screen/ErrorModal.tsx", "hash": "55c1bff6f433a3e4614fd6f7df567a99322e350b"}, {"file": "apps/candidates/screen/FullCalendarView.tsx", "hash": "d0ec285a3e583c87362416542fc04fd824439a55", "deps": ["recruitments", "api"]}, {"file": "apps/candidates/screen/JobsScreen.tsx", "hash": "a7a8be6a4c4ed73f1fe22ba94e10b39bb7c620d7", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/candidates/screen/RecruiterDashboard.tsx", "hash": "f892469ec269e728c6b22e1eb93c88dd33aecc1b"}, {"file": "apps/candidates/screen/SuccessfulModal.tsx", "hash": "26941b0488cda24a447303a1aaf670306a1f33d6"}, {"file": "apps/candidates/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/candidates/src/root.component.test.tsx", "hash": "ef1e9ce9aa95a15bdc7d000d4db1acccba108fdd"}, {"file": "apps/candidates/src/root.component.tsx", "hash": "86e2ba8d23b90abca16514dbe9834aee8eda7f36", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/candidates/src/urecruits-candidates.tsx", "hash": "23e936c0a9916f30e17053d6e8b5a5a7f0c1d4f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/candidates/store/index.ts", "hash": "931d6f6dcfbc57b73548f12b3a02b741b9715de7"}, {"file": "apps/candidates/store/reducers/candidateJobsReducer.ts", "hash": "1382e6da89cab891eedb1e5bac6b6be63e2137f2", "deps": ["recruitments"]}, {"file": "apps/candidates/store/reducers/candidateOffersReducer.ts", "hash": "ee12801407388debec40007af8b58dbf2dc658d5"}, {"file": "apps/candidates/store/reducers/candidateScoreboardReducer.ts", "hash": "4f134a72d237b93e0acf8916bb36e984018915df"}, {"file": "apps/candidates/store/reducers/companiesReducer.ts", "hash": "d035772d5cde4c06ea7509f72ae998f5f16a381c"}, {"file": "apps/candidates/store/reducers/companyJobsReducer.ts", "hash": "8e98981382f132d20de027d4037ba99ad4ca7ab0"}, {"file": "apps/candidates/store/reducers/domainAssessmentReducer.ts", "hash": "61950c5011a06a87b49b611fec742f8da8b334a6"}, {"file": "apps/candidates/styles/_about-company.scss", "hash": "73050719944b07848b4dfea2046d6e7a0b970a5f"}, {"file": "apps/candidates/styles/_calendarDayView.scss", "hash": "f53b80aee3b7f310b7564b3cdc1ad29f29f68300"}, {"file": "apps/candidates/styles/_candidate_job_scoreboard.scss", "hash": "7569a1011a8ff8ca6d16f485a8a43ac83857dd4c"}, {"file": "apps/candidates/styles/_candidate-jobs.scss", "hash": "004cc56eb0788d23943540cd2706c69e7ec4bfa4"}, {"file": "apps/candidates/styles/_candidate-offer-view.scss", "hash": "8ec94e1b730f966bc60fed9bfa49e67d8bfd0023"}, {"file": "apps/candidates/styles/_candidate-offers.scss", "hash": "13ad104c3442e3ea1ce58dcd6d7ec960dc7ecfa0"}, {"file": "apps/candidates/styles/_candidate-scoreboard.scss", "hash": "8866e1602cdebe265afe8cdd1492a2551149a2b6"}, {"file": "apps/candidates/styles/_companies-list.scss", "hash": "5c19b969d1175457afb906699ea57821b0409914"}, {"file": "apps/candidates/styles/_companies.scss", "hash": "944ca2bab14df0c817ad337ba037dbd387c76199"}, {"file": "apps/candidates/styles/_company-jobs-list.scss", "hash": "47cfb5efc9a93426c6d63d3ad8374feb37b3fb4b"}, {"file": "apps/candidates/styles/_company-jobs.scss", "hash": "cff4c7a2f62fc53de96ee17a6b16bfd0e7b5b277"}, {"file": "apps/candidates/styles/_config.scss", "hash": "ca498c144478ff5719d67cb11811ecbcb3d95102"}, {"file": "apps/candidates/styles/_custom-phone-input.scss", "hash": "38839746226fa485b7b22e05f58b1585b442d802"}, {"file": "apps/candidates/styles/_dashboard_loader.scss", "hash": "d9b75285b0be2d280498a8adaa7f374e0a7040d2"}, {"file": "apps/candidates/styles/_dashboards.scss", "hash": "4031128eca49e947ec10624c964c3256d504d718"}, {"file": "apps/candidates/styles/_dialog.scss", "hash": "9d61bef9564dc8c0199a9a0d8f6078e431d31e78"}, {"file": "apps/candidates/styles/_full-calendar-view.scss", "hash": "bb99619835bf8ca0d1b358fb3dfd40b9c32cdbba"}, {"file": "apps/candidates/styles/_gallery.scss", "hash": "e3ee2edadda129bf2e81d1347e3bcf935753ec77"}, {"file": "apps/candidates/styles/_hot-jobs.scss", "hash": "aac0d460a067e25dc58d5e044fbccc46fc9154fa"}, {"file": "apps/candidates/styles/_jobs-head.scss", "hash": "dcb7f86b5e82b0d099497c3a2844185b97efc12b"}, {"file": "apps/candidates/styles/_manage-assignment.scss", "hash": "784bff4fd7540031b3c539be46ca963c6b1c1db2"}, {"file": "apps/candidates/styles/_manage-team-popup.scss", "hash": "e5e70cfbf0250174051ed807b3d33b1acca62c21"}, {"file": "apps/candidates/styles/_manage-team.scss", "hash": "197fc9ea215aae182e5f6cf4b60f0470770b575f"}, {"file": "apps/candidates/styles/_mixins.scss", "hash": "cedc22de782924c2d12e3ac2c219ab0a45324084"}, {"file": "apps/candidates/styles/_popups.scss", "hash": "5573b3166bdabb4300c759889922c5a08d8701a4"}, {"file": "apps/candidates/styles/_recruitment.scss", "hash": "8bc54b1ddfc6639f5fb45b03630e6b849bc95276"}, {"file": "apps/candidates/styles/_signature_iframe.scss", "hash": "8178bf2220e6ee2eb025d9552324b328ca07bdbf"}, {"file": "apps/candidates/styles/_user-information-screen.scss", "hash": "71d79ea4503e5526cee2a1b06d23d7fcfdd3b94f"}, {"file": "apps/candidates/styles/_workflow.scss", "hash": "f9566977ce84fa5732c280be207e52cda2e3d1b7"}, {"file": "apps/candidates/styles/candidateAnswersheet/_domainAssessmentAnswers.scss", "hash": "770f3287367d355bd1c122132f48b3654f6b451c"}, {"file": "apps/candidates/styles/candidateAssessment/_domainAssessment.scss", "hash": "d68f59b903d941e1082c19e18f825f4cc60844d0"}, {"file": "apps/candidates/styles/candidateAssessment/_domainQuestion.scss", "hash": "d99a90290fc56c2e4da77749e5a89ec1fc4af9e8"}, {"file": "apps/candidates/styles/candidateAssessment/_pagination.scss", "hash": "c3e764e6940edf419d55c77ccb31cbe45ff6b169"}, {"file": "apps/candidates/styles/candidateAssessment/editor-modals.scss", "hash": "6afda1b462f5b128211429c8e0e0b75a9cdfa4d2"}, {"file": "apps/candidates/styles/candidateAssessment/instructionDiadlog.scss", "hash": "1319d093646f5af027cd9a78d5774ee8a2994551"}, {"file": "apps/candidates/styles/candidateAssessment/modal.scss", "hash": "816472094aa71d856f17dd1bfc66ac8377dd3b23"}, {"file": "apps/candidates/styles/datepicker/datepicker.scss", "hash": "9eb8b9803e8a097ba3407721b361a57cd3337e0a"}, {"file": "apps/candidates/styles/datepicker/mixins.scss", "hash": "43c27b9e87ec3739480e8a435891ab8e877eaf5f"}, {"file": "apps/candidates/styles/datepicker/variables.scss", "hash": "48109e34218223e6e229f60f29339fea9a17b78e"}, {"file": "apps/candidates/styles/main.scss", "hash": "51e37e3e477b09a3e5635851b8bd675606f96d87"}, {"file": "apps/candidates/styles/selectCustomBottomStyle.ts", "hash": "6d560e9f483bdef8ef72076c01ccf5de5f9b99b8"}, {"file": "apps/candidates/styles/selectCustomErrorStyle.ts", "hash": "f013a863158e4a270c7733101787a3690c6224da"}, {"file": "apps/candidates/styles/selectCustomStyle.ts", "hash": "e63def971cebc1a72140b32c324e4d06bbc6a6bb"}, {"file": "apps/candidates/styles/selectDisableStyle.ts", "hash": "118b5a21cd0ecc90b4789162d4e857ec01cde76a"}, {"file": "apps/candidates/styles/selectMobileMenuStyle.ts", "hash": "13ff3678aeb4d0ec44aa3867b15621f89fedeb03"}, {"file": "apps/candidates/styles/selectSmallStyle.ts", "hash": "46b7911912cec40fa541d9e9af239c39dfee0dc6"}, {"file": "apps/candidates/tsconfig.json", "hash": "ea2665cc46758d24ce059b0ecaea47255d68ddc6"}, {"file": "apps/candidates/types/global/global.ts", "hash": "e22f744e0670f0d7a762fec768821cf81ac66109"}, {"file": "apps/candidates/types/redux/candidateJobs.ts", "hash": "737209b6710148a5ef85f3e87c33426e7e785a05", "deps": ["recruitments"]}, {"file": "apps/candidates/types/redux/candidateOffers.ts", "hash": "162511fa8cd406ea5be61f74ba2c95ce8a8a6bdf"}, {"file": "apps/candidates/types/redux/candidateScoreboard.ts", "hash": "a6d6dd806ce3a616266bc8aa4cd709863fda1c44"}, {"file": "apps/candidates/types/redux/companies.ts", "hash": "08eba5e2b1165346d89700ba72bccecbd37dbdd3"}, {"file": "apps/candidates/types/redux/companyJobs.ts", "hash": "6a1b377598cad68106ae1fa4f45eff2df32a2b5e"}, {"file": "apps/candidates/types/redux/domainAssessment.ts", "hash": "4b8f1744d92a8e8023970a4966721134c566907b"}, {"file": "apps/candidates/types/redux/manage-team-members.ts", "hash": "95bdde87e6b9551b693e4723be61790c1fff4469"}, {"file": "apps/candidates/utils/checkLastAddedItemToSelect.ts", "hash": "60906bdbaf7df4199c3a15b016588e1e300664be"}, {"file": "apps/candidates/utils/constants.ts", "hash": "cb11b16ec20e4ec5b8d4d7ba8c038f118f317828"}, {"file": "apps/candidates/utils/rounds/checks.ts", "hash": "927f744d0dec16ccbaad6ea2cb0852438705d325"}, {"file": "apps/candidates/utils/rounds/index.ts", "hash": "4a822ad1440efac4e55470e9c9f6975e7dac95a5"}, {"file": "apps/candidates/utils/rounds/searchActive.ts", "hash": "a90cb7fc65e5a6eccc99c7e6330968c2995d2f84"}, {"file": "apps/candidates/utils/rounds/searchRound.ts", "hash": "1122a7b12ca952247cf78662e51aff9ea9fbd7ac"}, {"file": "apps/candidates/utils/sortingFuncForTables.ts", "hash": "5e1b0b21dddab470160821f23323161dae50461f"}, {"file": "apps/candidates/utils/transformDate.ts", "hash": "02e3d12a12f4e2e20255ea8a12bfc3557663b0f1"}, {"file": "apps/candidates/webpack.config.js", "hash": "1fe378251f073b893a0d55b273b7f37572a9dd03"}]}}, "header": {"name": "header", "type": "lib", "data": {"root": "apps/header", "sourceRoot": "apps/header", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/header/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/header/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/header/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/header/components/Chatbot/Chatbot.tsx", "hash": "4e439bdf3461643e758bc1267d0a89a9ee360d20"}, {"file": "apps/header/components/Chatbot/ChatHeader.tsx", "hash": "1c1c96dce0ef30a46e375c68f4c5922e21fa0171"}, {"file": "apps/header/components/Chatbot/config.tsx", "hash": "bfaa4939ed1bcf4f505f87d4ff4d54861271d7aa"}, {"file": "apps/header/components/Chatbot/MessageParser.tsx", "hash": "725d2f6482ef8759c67e45180295619c3ceeb5f9"}, {"file": "apps/header/components/Chatbot/PwBotAvatar.tsx", "hash": "e5ff5e9052e86e1cec076014679879079c9a1aed"}, {"file": "apps/header/components/Header.tsx", "hash": "cc4f4fb557904cea5a5454024f6aca011743d9e3", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/header/components/Loader.tsx", "hash": "f34cf859d2bcad7eb1ec7eed8f8866d833ac25b6"}, {"file": "apps/header/components/Login.tsx", "hash": "5af964ef659d4cc3b73c9fc92b81b227e8a47a09"}, {"file": "apps/header/components/MobileMenu.tsx", "hash": "196a4be96912d85fc1f114eba675435fc7bd59e2", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/header/components/MobileMenuItem.tsx", "hash": "202f45f5b8f7b4fa8b097060c6b2f4deb1e7b23e"}, {"file": "apps/header/components/Notifications.tsx", "hash": "59729b60d6859e4a1ff263d246a46b3d9180c2aa", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/header/image/avatar.svg", "hash": "a6dfdace3013cadec974a9b5e89b193268aa4c0a"}, {"file": "apps/header/image/icon/add-job_ic.svg", "hash": "1b764c5f3a07aa609d4a836bc44c8744fc977676"}, {"file": "apps/header/image/icon/assessment-domain_ic.svg", "hash": "0992bda62cc908bf7f2ca09d0f6d94c7918568b0"}, {"file": "apps/header/image/icon/bell_ic.svg", "hash": "e93a1aabdb4fe4f462443192d93cb5cddcc769dc"}, {"file": "apps/header/image/icon/bookmark.svg", "hash": "34cc7338c621bec4bec7795bbf8331d1ce3b594b"}, {"file": "apps/header/image/icon/calendar_ic.svg", "hash": "81d2b1b2c84fec48e199af69f7db0e23ea9a50bb"}, {"file": "apps/header/image/icon/candidate_ic.svg", "hash": "cd4048df6fd99f4db51cc7f882a856f190eed26a"}, {"file": "apps/header/image/icon/chat_ic.svg", "hash": "b71660bbb7be966feac1c6e038ed51c3620baaab"}, {"file": "apps/header/image/icon/Chatbot.svg", "hash": "02b8fcda0850be2899cd955216390508b69a959c"}, {"file": "apps/header/image/icon/check-circle_ic.svg", "hash": "****************************************"}, {"file": "apps/header/image/icon/clock_ic.svg", "hash": "4cf3cb7b2b6d6a9ebf72d7fa566cce7206b3b060"}, {"file": "apps/header/image/icon/close_ic.svg", "hash": "6cbd5e3f19cc05d8105802cf560ee71386235dcb"}, {"file": "apps/header/image/icon/coding_assessment.svg", "hash": "a3a5ad16cdeca6766034040fcd7dbc4be3f045f0"}, {"file": "apps/header/image/icon/compact-logo.svg", "hash": "65be841cb14118c3503155b2ef75ccd820e2cd6b"}, {"file": "apps/header/image/icon/companies.svg", "hash": "4b5915f1d29609be7a0a4fa415c47dae4dc593b8"}, {"file": "apps/header/image/icon/databases_ic.svg", "hash": "9caf9b5aed687b70de84f073a403d524920b46f2"}, {"file": "apps/header/image/icon/domain_ic.svg", "hash": "01cea63f72b5bc17a8dbb4c63b4d8b9761870c0e"}, {"file": "apps/header/image/icon/drugScreening.svg", "hash": "2604a82e6f1f5f73485677967cb2b47c13d40ada"}, {"file": "apps/header/image/icon/faq_ic.svg", "hash": "8c9200278e500b3b41866596bd473612f2847900"}, {"file": "apps/header/image/icon/feedback_ic.svg", "hash": "a4c10d748e1cfe1d76888344c8c27f55a622fa4b"}, {"file": "apps/header/image/icon/file_ic.svg", "hash": "0992bda62cc908bf7f2ca09d0f6d94c7918568b0"}, {"file": "apps/header/image/icon/folder_ic.svg", "hash": "e20c83c7d2c823679bdfe5b50b925ec83663892e"}, {"file": "apps/header/image/icon/home_ic.svg", "hash": "64dced54f215bd094c97d8dc37681d16cba06cc2"}, {"file": "apps/header/image/icon/Inetgration.svg", "hash": "08de1fcd6b5aa4cce337680ec34c2a6dae051e4c"}, {"file": "apps/header/image/icon/Interview.svg", "hash": "9ee719be92243359cb60cb18d76bf3d05b0a40c3"}, {"file": "apps/header/image/icon/jobs_ic.svg", "hash": "16b40a1e6818afef4f0a0b14f72f643a9cb42818"}, {"file": "apps/header/image/icon/light-arrow-down_ic.svg", "hash": "31d1ef328796caa5715c05f690b229410993803a"}, {"file": "apps/header/image/icon/logout_ic.svg", "hash": "977634e25191f890c331718c0946837a370bd807"}, {"file": "apps/header/image/icon/mail_ic.svg", "hash": "e790d9a22279e15e3ea8b2fb920f6e78051752fd"}, {"file": "apps/header/image/icon/mail.svg", "hash": "e790d9a22279e15e3ea8b2fb920f6e78051752fd"}, {"file": "apps/header/image/icon/manage-domain_ic.svg", "hash": "6c845340f2a3fb9dc96bf93d54cd1675f5b3f8a6"}, {"file": "apps/header/image/icon/match.svg", "hash": "54924f1c05f7dae62459005331ac78dab93c38dc"}, {"file": "apps/header/image/icon/more_ic.svg", "hash": "78a61e9fa6294c6e6dbbdff7f357fcee349b9424"}, {"file": "apps/header/image/icon/offericon.svg", "hash": "213b375147d996ffa2b4e89821f390e1aea587d5"}, {"file": "apps/header/image/icon/report_ic.svg", "hash": "cce5cd2e38f15cb4d31570186cc0229e3297dbd0"}, {"file": "apps/header/image/icon/review_ic.svg", "hash": "0ded0735bc60948012a1a8a8d36c2a5b71163405"}, {"file": "apps/header/image/icon/right-green-arrow_ic.svg", "hash": "4f2a039cc6dd04efaa4636a5be955e00d845b2bd"}, {"file": "apps/header/image/icon/screening-question_ic.svg", "hash": "c7a0bb992051c673aa5214522cc1f7d44ba7ac54"}, {"file": "apps/header/image/icon/screening.svg", "hash": "19cc4a57e3c82c826e9bae31af127d631a41edef"}, {"file": "apps/header/image/icon/settings_ic.svg", "hash": "118c381db9aa1d5c6f5493b6362cf3fcf6fe415f"}, {"file": "apps/header/image/icon/solid-arrow_ic.svg", "hash": "d08d31601fb9b7d5b6668142266260bd0aab6275"}, {"file": "apps/header/image/icon/star_ic.svg", "hash": "9d0e0975e996699552f11c480d00291322e0026d"}, {"file": "apps/header/image/icon/team_ic.svg", "hash": "886ab5e421b5336c97ee42515493cf97bf79c659"}, {"file": "apps/header/image/icon/UR_Agent.svg", "hash": "7685229ee7cc805fe661067734a181b4d1c329d0"}, {"file": "apps/header/image/icon/UR_Agent1.svg", "hash": "e4cf473a64382ef678315cf95219afbcd5010780"}, {"file": "apps/header/image/icon/user_ic.svg", "hash": "fa8728c740fec157a15fba9fb014edaa1be09ca6"}, {"file": "apps/header/image/icon/workflow_ic.svg", "hash": "d67f001f56e7b129022af7d22e54d8cc56aba961"}, {"file": "apps/header/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/header/package.json", "hash": "924c9c81bc93d43b68078f8e117a922a74f6acdb", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/header/src/declarations.d.ts", "hash": "437fcefe311959d74e3144ea4409ccd1539121ba"}, {"file": "apps/header/src/root.component.tsx", "hash": "5c3ef7d1fadabd8eed537f7bf0429f7a35c285d4", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/header/src/urecruits-header.tsx", "hash": "23e936c0a9916f30e17053d6e8b5a5a7f0c1d4f6", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/header/store/ActionProvider.tsx", "hash": "58f883015d3526b9ec292b6ad9a580e3ed183b13", "deps": ["@ucrecruits/globalstyle", "api"]}, {"file": "apps/header/styles/_chatbot.scss", "hash": "974716c82a636283f6d8a966e7192127c55010bf"}, {"file": "apps/header/styles/_config.scss", "hash": "ca498c144478ff5719d67cb11811ecbcb3d95102"}, {"file": "apps/header/styles/_header.scss", "hash": "c390b05efa1121868c861a625107a848ef50373a"}, {"file": "apps/header/styles/_mixins.scss", "hash": "b6bf484ee19867685952de2824e114c269f91f56"}, {"file": "apps/header/styles/_mobile-menu.scss", "hash": "3131d05662d117e5928c2d53bd2513cf3ce25233"}, {"file": "apps/header/styles/main.scss", "hash": "263b4f52316c286c9de7429276334f474a93f528"}, {"file": "apps/header/tsconfig.json", "hash": "32fb8cc2735bb2a549ea8467d781f7d3dba783ca"}, {"file": "apps/header/util/firebase.js", "hash": "a4f666c244715c09261020f97a59dd2151464c02"}, {"file": "apps/header/util/requestNotification.ts", "hash": "81b5c5dbd7c6143075248e0ef712575992a65237", "deps": ["api", "@ucrecruits/globalstyle"]}, {"file": "apps/header/webpack.config.js", "hash": "c8a20074e1bf4d787fca8b86eb3787804a0ec696"}]}}, "aside": {"name": "aside", "type": "lib", "data": {"root": "apps/aside", "sourceRoot": "apps/aside", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "prepare": {"executor": "@nrwl/workspace:run-script", "options": {"script": "prepare"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/aside/.gitignore", "hash": "bd280ec42a5bbd64d7495d6b0b8d9d78c906d906"}, {"file": "apps/aside/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/aside/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/aside/hook/useResponsiveDetailMenu.ts", "hash": "8f96d36a8dbac493ec7fc0f14c53e81cb2455b87"}, {"file": "apps/aside/image/icon/add-job_ic.svg", "hash": "795a03402348c784dc2927190b94c69370432655"}, {"file": "apps/aside/image/icon/assessment-domain_ic.svg", "hash": "9b596afc8ed5e936ee590337576413d6d194ee8e"}, {"file": "apps/aside/image/icon/background.svg", "hash": "d8b963f5148aef08e921eb8d4cfc163ebe892b52"}, {"file": "apps/aside/image/icon/bookmark.svg", "hash": "3eeec9752798c2d4b39442b4d8cab0103f1fd8fb"}, {"file": "apps/aside/image/icon/calendar_ic.svg", "hash": "59b1da564a8d786cc1baa760c2c8232a3ebbd924"}, {"file": "apps/aside/image/icon/candidate_ic.svg", "hash": "516e350ebc923011230a563be1fa89c04621227f"}, {"file": "apps/aside/image/icon/chat_ic.svg", "hash": "819d5c0761f7900998ae3f7c28e9a1f543e5eab5"}, {"file": "apps/aside/image/icon/check-circle_ic.svg", "hash": "****************************************"}, {"file": "apps/aside/image/icon/clock_ic.svg", "hash": "648ee9ab2c3e12d30aaf74b9aaaf8ec9df00cea8"}, {"file": "apps/aside/image/icon/close_ic.svg", "hash": "6cbd5e3f19cc05d8105802cf560ee71386235dcb"}, {"file": "apps/aside/image/icon/coding_assessment.svg", "hash": "770733897416e25b39fbf3bd80234ddccc00613a"}, {"file": "apps/aside/image/icon/companies.svg", "hash": "b06c2b811e6f7874af3fe5c2b8b0587196a9c2e3"}, {"file": "apps/aside/image/icon/databases_ic.svg", "hash": "66cd80a1cedb845d578ec7a91b7303318570334b"}, {"file": "apps/aside/image/icon/domain_ic.svg", "hash": "0790a57f78d1ac7ba13a15589d5543c158be1785"}, {"file": "apps/aside/image/icon/Drug.svg", "hash": "5226e49a4c26374be4e9a19294e8b8128eb9eb13"}, {"file": "apps/aside/image/icon/faq_ic.svg", "hash": "0e6558d1879adbb688d3315d2eeb468f4ef11d65"}, {"file": "apps/aside/image/icon/feedback_ic.svg", "hash": "b4165d360aeccba3bcbb6c380febab9c44e249b1"}, {"file": "apps/aside/image/icon/file_ic.svg", "hash": "9b596afc8ed5e936ee590337576413d6d194ee8e"}, {"file": "apps/aside/image/icon/files_ic.svg", "hash": "822fadb3adc9a2199f9ca62ba4ca1b243540d4ec"}, {"file": "apps/aside/image/icon/folder_ic.svg", "hash": "7d276eba59eabd84605810f208a4cb6ead0a9fa8"}, {"file": "apps/aside/image/icon/home_ic.svg", "hash": "58e7d7e47627fe60faec45bc1fd288fa426c7303"}, {"file": "apps/aside/image/icon/Inetgration.svg", "hash": "27ab0b653c80cb5c2e8d76a962d9d9079ddd7dbe"}, {"file": "apps/aside/image/icon/Interview.svg", "hash": "8d92308471257010bb381617f68b13e6ed23a504"}, {"file": "apps/aside/image/icon/jobs_ic.svg", "hash": "95a51506191bdcb31ef6eab1a3a3000913150d79"}, {"file": "apps/aside/image/icon/mail.svg", "hash": "cc44035504defb250687512f4a945dc12cf7127e"}, {"file": "apps/aside/image/icon/manage-domain_ic.svg", "hash": "1ce6d59deb4b5485befd70887559e561d77da999"}, {"file": "apps/aside/image/icon/more_ic.svg", "hash": "2c08340088a70be043394a6bf6d29b377cc416d9"}, {"file": "apps/aside/image/icon/offer.svg", "hash": "14c7ba8fd2ab76a1e0213c7b870d6351944de038"}, {"file": "apps/aside/image/icon/report_ic.svg", "hash": "cce5cd2e38f15cb4d31570186cc0229e3297dbd0"}, {"file": "apps/aside/image/icon/review_ic.svg", "hash": "155b41a5da7a6a081070572946f9a83f592f5c8b"}, {"file": "apps/aside/image/icon/right-green-arrow_ic.svg", "hash": "4f2a039cc6dd04efaa4636a5be955e00d845b2bd"}, {"file": "apps/aside/image/icon/screening-question_ic.svg", "hash": "e504b0188ea5f606057b894a163ec0ea4963c334"}, {"file": "apps/aside/image/icon/star_ic.svg", "hash": "7efe172799bdfb0f9032108893b6e47a7e1ac082"}, {"file": "apps/aside/image/icon/team_ic.svg", "hash": "8ad21370c3f2766615e36a584cbb2f51b9285c5e"}, {"file": "apps/aside/image/icon/workflow_ic.svg", "hash": "9b34b39866a4164436d9e7e59d6696b0b34fde08"}, {"file": "apps/aside/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/aside/package.json", "hash": "7465a625502d44761fcb583480ffc38aa2bc2e7c", "deps": ["@ucrecruits/globalstyle", "npm:typescript"]}, {"file": "apps/aside/src/Components/CandidateSidebar/CandidateSidebar.tsx", "hash": "ff7eaebc9fb9494d270a7eb289c74f07b9dcf590", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/aside/src/Components/CandidateSidebar/index.ts", "hash": "0b7e7ff295e1b25524394ab3fe5aaf729ebe9875"}, {"file": "apps/aside/src/Components/CompanySidebar/CompanySidebar.tsx", "hash": "c568308e26f6803b9aff64a6c600c56417f89d78", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/aside/src/Components/CompanySidebar/index.ts", "hash": "3ce4cb7e38797d4abb52539e3fd29af4e8ed8a26"}, {"file": "apps/aside/src/Components/MoreMenu/index.ts", "hash": "5f4a20e91b4279b69690641b342cbf05e9aa1a62"}, {"file": "apps/aside/src/Components/MoreMenu/MoreMenu.tsx", "hash": "59f57f8a5fb38a07534793a197614a87c2bcbe05"}, {"file": "apps/aside/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/aside/src/root.component.tsx", "hash": "5b7bcfd7bf18977a5dc438e204362216db4d1811", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/aside/src/urecruits-aside.tsx", "hash": "5f1368b6adce1b40ed34f6dc2524e9c091dd1c2a", "deps": ["@ucrecruits/globalstyle"]}, {"file": "apps/aside/styles/_aside.scss", "hash": "8ac947957ecc3c0126e855fc9378a84e5fa73ec7"}, {"file": "apps/aside/styles/_config.scss", "hash": "ca498c144478ff5719d67cb11811ecbcb3d95102"}, {"file": "apps/aside/styles/_mixins.scss", "hash": "6d5d1e0ec55e7218d19ec8c574a576db27af5ac4"}, {"file": "apps/aside/styles/main.scss", "hash": "ba32bf815e2d06321cc4c8ff0fceaf6f64f5e983"}, {"file": "apps/aside/tsconfig.json", "hash": "f73a32bcc3a99cf6d762542091161bf6cd1733c3"}, {"file": "apps/aside/webpack.config.js", "hash": "6bdb324a451a550bb5bdcea2225dbe11bafba282"}]}}, "api": {"name": "api", "type": "lib", "data": {"root": "apps/api", "sourceRoot": "apps/api", "targets": {"start": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start"}}, "start:standalone": {"executor": "@nrwl/workspace:run-script", "options": {"script": "start:standalone"}}, "build": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build"}}, "build:webpack": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:webpack"}}, "analyze": {"executor": "@nrwl/workspace:run-script", "options": {"script": "analyze"}}, "lint": {"executor": "@nrwl/workspace:run-script", "options": {"script": "lint"}}, "format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "format"}}, "check-format": {"executor": "@nrwl/workspace:run-script", "options": {"script": "check-format"}}, "test": {"executor": "@nrwl/workspace:run-script", "options": {"script": "test"}}, "watch-tests": {"executor": "@nrwl/workspace:run-script", "options": {"script": "watch-tests"}}, "coverage": {"executor": "@nrwl/workspace:run-script", "options": {"script": "coverage"}}, "build:types": {"executor": "@nrwl/workspace:run-script", "options": {"script": "build:types"}}}, "tags": [], "files": [{"file": "apps/api/.env.example", "hash": "4211c936e5492c4594f4bb5ce0d1dcc26d186cbc"}, {"file": "apps/api/.gitignore", "hash": "ee19c0b525bb0a47d977daba83cc0a81e276a3ff"}, {"file": "apps/api/.prettierignore", "hash": "0b237bba224c5cab59323db39a814bd1b0be3e3c"}, {"file": "apps/api/babel.config.json", "hash": "0ebfe566de3b6517784c6a5d552a0b5de900fbc6"}, {"file": "apps/api/jest.config.js", "hash": "b47aa4a2e31c6348bb57c1b28b324b4e7687af7a"}, {"file": "apps/api/package.json", "hash": "1ffc51148466e0021b0b5c6b67ae7e5bae327d8e", "deps": ["npm:typescript"]}, {"file": "apps/api/src/declarations.d.ts", "hash": "facd5c8e8482585dc2b7d8bc7d56923be7966886"}, {"file": "apps/api/src/root.component.tsx", "hash": "f9375f8b100e689ba97844ea5edf14e7a24a2e6f"}, {"file": "apps/api/src/urecruits-api.tsx", "hash": "bf9cb11f01707b02e14f39cecfe88a2828b3832e"}, {"file": "apps/api/tsconfig.json", "hash": "14db215dbb187a35e102245e2e4ebd77e28fcf6a"}, {"file": "apps/api/webpack.config.js", "hash": "91c27be3477782c62920ecbe64df882a1e421785"}]}}}, "externalNodes": {"npm:country-state-city": {"type": "npm", "name": "npm:country-state-city", "data": {"version": "^3.2.1", "packageName": "country-state-city"}}, "npm:@nrwl/workspace": {"type": "npm", "name": "npm:@nrwl/workspace", "data": {"version": "13.4.3", "packageName": "@nrwl/workspace"}}, "npm:@nrwl/cli": {"type": "npm", "name": "npm:@nrwl/cli", "data": {"version": "13.4.3", "packageName": "@nrwl/cli"}}, "npm:@nrwl/tao": {"type": "npm", "name": "npm:@nrwl/tao", "data": {"version": "13.4.3", "packageName": "@nrwl/tao"}}, "npm:typescript": {"type": "npm", "name": "npm:typescript", "data": {"version": "4.2.4", "packageName": "typescript"}}}, "dependencies": {"coding-assessments": [{"source": "coding-assessments", "target": "api", "type": "static"}, {"source": "coding-assessments", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "coding-assessments", "target": "npm:typescript", "type": "static"}], "manage-assignment": [{"source": "manage-assignment", "target": "api", "type": "static"}, {"source": "manage-assignment", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "manage-assignment", "target": "npm:typescript", "type": "static"}], "profile-setting": [{"source": "profile-setting", "target": "api", "type": "static"}, {"source": "profile-setting", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "profile-setting", "target": "npm:typescript", "type": "static"}], "manage-domain": [{"source": "manage-domain", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "manage-domain", "target": "api", "type": "static"}, {"source": "manage-domain", "target": "npm:typescript", "type": "static"}], "live-coding": [{"source": "live-coding", "target": "api", "type": "static"}, {"source": "live-coding", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "live-coding", "target": "coding-assessments", "type": "static"}, {"source": "live-coding", "target": "npm:typescript", "type": "static"}], "create-job": [{"source": "create-job", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "create-job", "target": "api", "type": "static"}, {"source": "create-job", "target": "recruitments", "type": "static"}, {"source": "create-job", "target": "npm:typescript", "type": "static"}], "hr-analytics": [{"source": "hr-analytics", "target": "api", "type": "static"}, {"source": "hr-analytics", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "hr-analytics", "target": "recruitments", "type": "static"}, {"source": "hr-analytics", "target": "npm:typescript", "type": "static"}], "recruitments": [{"source": "recruitments", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "recruitments", "target": "api", "type": "static"}, {"source": "recruitments", "target": "live-coding", "type": "static"}, {"source": "recruitments", "target": "npm:typescript", "type": "static"}], "@ucrecruits/globalstyle": [{"source": "@ucrecruits/globalstyle", "target": "npm:typescript", "type": "static"}, {"source": "@ucrecruits/globalstyle", "target": "api", "type": "static"}, {"source": "@ucrecruits/globalstyle", "target": "npm:country-state-city", "type": "static"}], "root-config": [{"source": "root-config", "target": "npm:typescript", "type": "static"}], "candidates": [{"source": "candidates", "target": "api", "type": "static"}, {"source": "candidates", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "candidates", "target": "live-coding", "type": "static"}, {"source": "candidates", "target": "npm:typescript", "type": "static"}, {"source": "candidates", "target": "recruitments", "type": "static"}], "header": [{"source": "header", "target": "api", "type": "static"}, {"source": "header", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "header", "target": "npm:typescript", "type": "static"}], "aside": [{"source": "aside", "target": "@ucrecruits/globalstyle", "type": "static"}, {"source": "aside", "target": "npm:typescript", "type": "static"}], "api": [{"source": "api", "target": "npm:typescript", "type": "static"}]}}